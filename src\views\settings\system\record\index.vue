<template>
  <v-container class="mt-4">
    <v-card class="page-card">
      <v-card-title>
        <h4>{{ $t("system_settings") }}</h4>
      </v-card-title>
      <v-card-text>
        <ValidationObserver ref="form" v-slot="{ handleSubmit }">
          <v-form
            @submit.prevent="handleSubmit(handleConfirm)"
            class="mt-4 px-4"
          >
            <v-row>
              <v-col cols="12" md="4">
                <FormGroup
                  rules="required"
                  name="taxRatio"
                  errorName="TaxRatio"
                >
                  <template #default="{ attrs }">
                    <InputNumber
                      v-bind="attrs"
                      id="taxRatio"
                      v-model="form.taxRatio"
                      :label="$t('taxRatio')"
                    />
                  </template>
                </FormGroup>
              </v-col>
              <v-col cols="12" md="4">
                <FormGroup
                  rules="required"
                  name="commissionRatio"
                  errorName="CommissionRatio"
                >
                  <template #default="{ attrs }">
                    <InputNumber
                      v-bind="attrs"
                      id="commissionRatio"
                      v-model="form.commissionRatio"
                      :label="$t('commissionRatio')"
                    />
                  </template>
                </FormGroup>
              </v-col>
            </v-row>
            <ExpansionPanels class="accordion">
                    <ExpansionPanelsItem>
                <template #header>
                  <div>
                    <v-icon class="px-2"> mdi-credit-card </v-icon>
                    <span>easy pay</span>
                  </div>
                </template>
                <template #content>
                  <v-row class="mt-4">
                    <v-col cols="12" md="4">
                      <FormGroup
                        rules="required"
                        name="brand"
                        errorName="Brand"
                      >
                        <template #default="{ attrs }">
                          <InputText
                            v-bind="attrs"
                            id="brand"
                            v-model="form.brand"
                            :label="$t('brand')"
                          />
                        </template>
                      </FormGroup>
                    </v-col>
                    <v-col cols="12" md="4">
                      <FormGroup
                        rules="required"
                        name="numberEasyPay"
                        errorName="NumberEasyPay"
                      >
                        <template #default="{ attrs }">
                          <InputText
                            v-bind="attrs"
                            id="numberEasyPay"
                            v-model="form.numberEasyPay"
                            :label="$t('numberEasyPay')"
                          />
                        </template>
                      </FormGroup>
                    </v-col>
                 
                
                    <v-col cols="12" md="4">
                      <FormGroup
                        rules="required"
                        name="expiryDate"
                        errorName="expiryDate"
                      >
                        <template #default="{ attrs }">
                          <InputText
                            v-bind="attrs"
                            id="expiryDate"
                            v-model="form.expiryDate"
                            :label="$t('expiryDate')"
                          />
                        </template>
                      </FormGroup>
                    </v-col>
                  
                  </v-row>
                </template>
              </ExpansionPanelsItem>
              <ExpansionPanelsItem>
                <template #header>
                  <div>
                    <v-icon class="px-2"> mdi-credit-card </v-icon>
                    <span>moyaser</span>
                  </div>
                </template>
                <template #content>
                  <v-row class="mt-4">
                  
                
                    <v-col cols="12" md="6">
                      <FormGroup rules="required" name="pk" errorName="PK">
                        <template #default="{ attrs }">
                          <InputText
                            v-bind="attrs"
                            id="pk"
                            v-model="form.pk"
                            :label="$t('pk')"
                          />
                        </template>
                      </FormGroup>
                    </v-col>
                    <v-col cols="12" md="6">
                      <FormGroup rules="required" name="sk" errorName="SK">
                        <template #default="{ attrs }">
                          <InputText
                            v-bind="attrs"
                            id="sk"
                            v-model="form.sk"
                            :label="$t('sk')"
                          />
                        </template>
                      </FormGroup>
                    </v-col>
               
                    <v-col cols="12" md="4">
                      <v-switch
                        :label="$t('visa_activate')"
                        hide-details
                        v-model="form.visamasterIsActive"
                      ></v-switch>
                    </v-col>
                    <v-col cols="12" md="4">
                      <v-switch
                        :label="$t('apple_activate')"
                        hide-details
                        v-model="form.appleIsActive"
                      ></v-switch>
                    </v-col>
                    <v-col cols="12" md="4">
                      <v-switch
                        :label="$t('stc_activate')"
                        hide-details
                        v-model="form.stcIsActive"
                      ></v-switch>
                    </v-col>
                  </v-row>
                </template>
              </ExpansionPanelsItem>
              <ExpansionPanelsItem>
                <template #header>
                  <div>
                    <v-icon class="px-2"> mdi-credit-card </v-icon>
                    <span>tamara</span>
                  </div>
                </template>
                <template #content>
                  <v-row class="mt-4">
                    <v-col cols="12" md="4">
                      <FormGroup
                        rules="required"
                        name="tamaraUrl"
                        errorName="tamaraUrl"
                      >
                        <template #default="{ attrs }">
                          <InputText
                            v-bind="attrs"
                            id="tamaraUrl"
                            v-model="form.tamaraUrl"
                            type="url"
                            :label="$t('tamaraUrl')"
                          />
                        </template>
                      </FormGroup>
                    </v-col>
                    <v-col cols="12" md="4">
                      <InputText
                        v-bind="attrs"
                        id="tamaraMerchantCode"
                        v-model="form.tamaraMerchantCode"
                        :label="$t('tamaraMerchantCode')"
                      />
                    </v-col>
                      <v-col cols="12" md="4">
                      <v-switch
                        :label="$t('tamara_activate')"
                        hide-details
                        v-model="form.tamaraIsActive"
                      ></v-switch>
                    </v-col>
                    <v-col cols="12" md="12">
                      <FormGroup
                        rules="required"
                        name="tamaraToken"
                        errorName="tamaraToken"
                      >
                        <template #default="{ attrs }">
                          <InputTextArea
                            v-bind="attrs"
                            id="tamaraToken"
                            v-model="form.tamaraToken"
                            :label="$t('tamaraToken')"
                          />
                        </template>
                      </FormGroup>
                    </v-col>

                  
                  </v-row>
                </template>
              </ExpansionPanelsItem>
              <ExpansionPanelsItem>
                <template #header>
                  <div>
                    <v-icon class="px-2"> mdi-credit-card </v-icon>
                    <span>tabby</span>
                  </div>
                </template>
                <template #content>
                  <v-row class="mt-4">
                    <v-col cols="12" md="4">
                      <FormGroup
                        rules="required"
                        name="tabbyUrl"
                        errorName="tabbyUrl"
                      >
                        <template #default="{ attrs }">
                          <InputText
                            v-bind="attrs"
                            id="tamaraUrl"
                            v-model="form.tabbyUrl"
                            type="url"
                            :label="$t('tabbyUrl')"
                          />
                        </template>
                      </FormGroup>
                    </v-col>
                    <v-col cols="12" md="4">
                      <InputText
                        id="tabbyMerchantCode"
                        v-model="form.tabbyMerchantCode"
                        :label="$t('tabbyMerchantCode')"
                      />
                    </v-col>
                     <v-col cols="12" md="4">
                      <v-switch
                        :label="$t('tabby_activate')"
                        hide-details
                        v-model="form.tabbyIsActive"
                      ></v-switch>
                    </v-col>
                    <v-col cols="12" md="12">
                      <FormGroup
                        rules="required"
                        name="tabbySecretKey"
                        errorName="tabbySecretKey"
                      >
                        <template #default="{ attrs }">
                          <InputTextArea
                            v-bind="attrs"
                            id="tabbySecretKey"
                            v-model="form.tabbySecretKey"
                            :label="$t('tabbySecretKey')"
                          />
                        </template>
                      </FormGroup>
                    </v-col>
                  </v-row>
                </template>
              </ExpansionPanelsItem>
            </ExpansionPanels>
          </v-form>
        </ValidationObserver>
      </v-card-text>
      <v-card-actions class="d-flex justify-end">
        <v-btn
          class="primary"
          min-width="150"
          @click="handleConfirm"
          :loading="loading"
          >{{ $t("save") }}</v-btn
        >
      </v-card-actions>
    </v-card>
  </v-container>
</template>

<script>
export default {
  name: "SystemRecord",
  data() {
    return {
      loading: false,
      form: {
        taxRatio: 0,
        commissionRatio: 0,
        pk: "",
        sk: "",
      },
    };
  },
  methods: {
    getSettings() {
      this.$http
        .get({
          reqName: "system-setting/get-for-admin",
        })
        .then(({ data: response }) => {
          this.form = response.data;
        });
    },
    handleConfirm() {
      this.$refs.form.reset();
      this.$refs.form.validate().then((success) => {
        if (success && !this.serverErrors) {
          this.loading = true;
          this.$http
            .put({
              reqName: "system-setting/update",
              data: this.generatePayload(),
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
    generatePayload() {
      const formData = new FormData();
      for (const key in this.form) {
        formData.append(key, this.form[key]);
      }
      return formData;
    },
  },
  mounted() {
    this.getSettings();
  },
};
</script>