export const reservationsUnCompRoutes = {
  path: "/reservations-un-comp",
  to: "/reservations-un-comp",
  icon: "mdi-file-document-multiple-outline",
  activeRoutes: ["reservations_un_comp_record"],
  meta: {
    title: "reservations-un-comp",
  },
  showInMenu: true,
  order: 8,
  component: () => import("@/views/reservations-un-comp"),
  children: [
    {
      path: "/",
      component: () => import("@/views/reservations-un-comp/record"),
      name: "reservations_un_comp_record",
      icon: "mdi-file-document-multiple-outline",
      to: "/reservations-un-comp",
      allowed: false,
      meta: {
        title: "reservations_un_comp_record",
      },
    },
    {
      path: "/reservations-un-comp/:reservationGUID",
      name: "ReservationUnCompDetails",
      component: () => import("@/views/reservations-un-comp/reservationDetails"),
    },
  ],
};
