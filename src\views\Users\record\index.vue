<template>
  <main class="mt-4">
    <v-container>
      <v-card class="page-card">
        <v-card-text>
          <v-row>
            <v-col cols="12">
              <CustomTable
                v-if="rendering"
                :tableHeaders="headers"
                :reqName="`${url}/paginated?GetActiveOnly=false&GetUsers=true`"
                :slotColumnNames="['#', 'isActive', 'actions']"
                :title="$t('users')"
              >
                <template #top-right>
                  <UserForm :url="url" />
                </template>
                <template #top-left>
                  <InputText
                    v-model="search"
                    name="search"
                    hide-details
                    outlined
                    class="mx-4"
                    dense
                    clearable
                    :label="$t('search')"
                    @input="setSearchQuery"
                  >
                  </InputText>
                  <!-- <v-btn
                    color="primary"
                    class="white--text"
                    depressed
                    @click="setSearchQuery"
                    min-width="80"
                    >{{ $t("search") }}</v-btn
                  > -->
                </template>
                <template #item.#="{ item, index }">
                  {{ index + pagination.from }}
                </template>
                <template #item.isActive="{ item }">
                  <toggle-service
                    :is-edit="true"
                    :model-name="`${url}/activate-deactivate`"
                    :model-id="item.id"
                    v-model="item.isActive"
                  />
                </template>
                <template #item.actions="{ item }">
                  <div class="d-flex align-center justify-center">
                    <UserForm :url="url" :id="item.id" />
                    <ImageDialog
                      :image="item.image"
                      url="user/add-image"
                      deleteUrl="user/delete-image"
                      :hasDeleteOption="true"
                      :id="item.id"
                      @update-table="updateTable"
                    />
                  </div>
                </template>
              </CustomTable>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-container>
  </main>
</template>



<script>
import UserForm from "@/components/Forms/User";
import update from "@/mixins/update";
import { mapActions } from "vuex";
export default {
  name: "UsersRecord",
  components: {
    UserForm,
  },
  mixins: [update],
  data() {
    return {
      rendering: true,
      url: "user",
      search: "",
    };
  },
  computed: {
    headers() {
      const headers = [
        {
          name: "#",
          value: "#",
          showColumn: true,
        },
        {
          name: "name",
          value: "name",
          sortable: false,
          showColumn: true,
        },
        {
          name: "email",
          value: "email",
          sortable: false,
          showColumn: true,
        },
        {
          name: "phone",
          value: "phone",
          sortable: false,
          showColumn: true,
        },
        // {
        //   name: "roleName",
        //   value: "roleName",
        //   sortable: false,
        //   showColumn: true,
        // },
        {
          name: "active",
          value: "isActive",
          sortable: false,
          showColumn: true,
        },

        {
          name: "actions",
          value: "actions",
          showColumn: true,
        },
      ];
      return headers.filter((head) => head.showColumn);
    },
  },
  methods: {
    ...mapActions(["setQuery"]),
    setSearchQuery() {
      console.log(this.search);
      let query = {
        SearchString: this.search,
      };
      this.setQuery(query);
      // this.rendering = false;
      // setTimeout(() => {
      //   this.rendering = true;
      // }, 10);
    },
  },
};
</script>