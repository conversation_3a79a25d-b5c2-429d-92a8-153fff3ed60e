export const systemVariablesRoutes = {
  path: "/",
  to: "/",
  icon: "mdi-keyboard",
  activeRoutes: ["record_countries", "record_cities", "record_airports", "record_airlines"],
  meta: {
    title: "systemVariables",
    // permissions: "",
  },
  order: 2,
  allow_children: true,
  showInMenu: true,
  name: "systemVariables",
  component: () => import("@/views/systemVariables"),
  children: [
    {
      path: "/countries",
      to: "/countries",
      component: () => import("@/views/systemVariables/countries/index"),
      name: "countries",
      icon: "mdi-flag",
      allowed: true,
      meta: {
        title: "countries",
        // permissions: 7
      },
      activeRoutes: ["record_countries"], // Specify the activeRoutes for the "cities" route

      children: [
        {
          path: "",
          component: () => import("@/views/systemVariables/countries/record/index"),
          name: "record_countries",
          icon: "arrow.svg",
          to: "/countries",
          allowed: true,
          meta: {
            title: "record_countries",
            // permissions: 7
          },
        }

      ],
    },
    {
      path: "/cities",
      to: "/cities",
      component: () => import("@/views/systemVariables/cities/index"),
      name: "cities",
      icon: "mdi-city",
      allowed: true,
      meta: {
        title: "cities",
        // permissions: 7
      },
      activeRoutes: ["record_cities"], // Specify the activeRoutes for the "cities" route

      children: [
        {
          path: "",
          component: () => import("@/views/systemVariables/cities/record/index"),
          name: "record_cities",
          icon: "arrow.svg",
          to: "/cities",
          allowed: true,
          meta: {
            title: "record_cities",
            // permissions: 7
          },
        }

      ],
    },
    {
      path: "/airports",
      to: "/airports",
      component: () => import("@/views/systemVariables/airports/index"),
      name: "airports",
      icon: "mdi-airport",
      allowed: true,
      meta: {
        title: "airports",
        // permissions: 7
      },
      activeRoutes: ["record_airports"], // Specify the activeRoutes for the "cities" route

      children: [
        {
          path: "",
          component: () => import("@/views/systemVariables/airports/record/index"),
          name: "record_airports",
          icon: "arrow.svg",
          to: "/airports",
          allowed: true,
          meta: {
            title: "record_airports",
            // permissions: 7
          },
        }

      ],
    },
    {
      path: "/airlines",
      to: "/airlines",
      component: () => import("@/views/systemVariables/airlines/index"),
      name: "airlines",
      icon: "mdi-airplane",
      allowed: true,
      meta: {
        title: "airlines",
        // permissions: 7
      },
      activeRoutes: ["record_airlines"], // Specify the activeRoutes for the "cities" route

      children: [
        {
          path: "",
          component: () => import("@/views/systemVariables/airlines/record/index"),
          name: "record_airlines",
          icon: "arrow.svg",
          to: "/airlines",
          allowed: true,
          meta: {
            title: "record_airlines",
            // permissions: 7
          },
        }

      ],
    },



  ]
};
