<template>
  <div>
    <ValidationObserver ref="form" v-slot="{ handleSubmit }">
      <v-form @submit.prevent="handleSubmit(handleConfirm)" class="mt-4 px-4">
        <v-row>
          <v-col cols="12" class="py-0">
            <FormGroup rules="required" name="password" errorName="password">
              <template #default="{ attrs }">
                <InputPassword
                  v-bind="attrs"
                  name="password"
                  :label="$t('password')"
                  v-model="form.password"
                />
              </template>
            </FormGroup>
          </v-col>
          <v-col cols="12">
            <FormGroup
              rules="required"
              name="confirmPassword"
              errorName="ConfirmPassword"
            >
              <template #default="{ attrs }">
                <InputPassword
                  v-bind="attrs"
                  name="confirmPassword"
                  :label="$t('confirmPassword')"
                  v-model="form.confirmPassword"
                />
              </template>
            </FormGroup>
          </v-col>
        </v-row>

        <div class="d-flex justify-end buttons-container gap-md mt-4">
          <v-btn
            :loading="loading"
            color="primary"
            type="submit"
            min-width="120"
            class="create-btn"
            >{{ $t("confirm") }}</v-btn
          >
          <v-btn
            outlined
            color="primary"
            min-width="120"
            @click="$emit('close-dialog')"
            >{{ $t("cancel") }}</v-btn
          >
        </div>
      </v-form>
    </ValidationObserver>
  </div>
</template>

<script>
export default {
  data() {
    return {
      form: {
        password: null,
        confirmPassword: null,
      },
      loading: false,
    };
  },

  methods: {
    handleConfirm() {
      this.$refs.form.validate().then((success) => {
        if (success && !this.serverErrors) {
          this.updatePassword();
        }
      });
    },
    updatePassword() {
      this.loading = true;
      this.$http
        .post({
          reqName: "auth/reset-password",
          data: this.generateData(),
        })
        .then(() => {
          this.$store.commit("SET_USERNAME", null);
          this.$store.commit("SET_RESET_TOKEN", null);
          this.$emit("close-dialog");
          this.$emit("next-step");
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    generateData() {
      const formData = new FormData();
      formData.append("password", this.form.password);
      return formData;
    },
  },
};
</script>