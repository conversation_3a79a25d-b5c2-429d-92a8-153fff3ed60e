.statistics{
  border-radius: 16px !important;
  background-color: #fff;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: unset !important;
  &:hover{
    box-shadow: 0 0 25px rgba(33,37,41,0.1) !important;
    transform: translateY(-5px);
    transition: all 0.4s ease;
  }
  &__body{
    padding: 15px 0 15px 20px;
    position: relative;
    flex: 1;
 
    border-radius: 0 !important;
    // &::after{
    //   content:"";
    //   position: absolute;
    //   left: 0;
    //   top: 0;
    //   height: 100%;
    //   width: 3px;
    //   background:#0da487;

    // }
    &--media{
      span{
        color:#9a9a9a ;
      }
      h4{
        font-size: 26px;
        margin-top: 5px;
        font-weight: 600;
      }

    }
    &--icon{
      width: 45px;
      height: 45px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6px;
      margin-left: 16px;
      background-color: rgba(13,164,135,0.1);

    }
  }
}