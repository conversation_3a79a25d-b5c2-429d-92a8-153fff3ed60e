<template>
  <section>
    <v-btn min-width="20px" text v-if="id" @click="handleShow()">
      <v-icon color="primary"> mdi-pencil</v-icon>
    </v-btn>
    <v-btn @click="dialog = true" color="primary" depressed class="mb-4" v-else>
      {{ $t("new_airport") }}
    </v-btn>
    <CustomDialog :dialog="dialog" @close-dialog="dialog = false">
      <template #header>
        <DialogHeader :title="$t('new_airport')" />
      </template>
      <ValidationObserver ref="form" v-slot="{ handleSubmit }">
        <v-form @submit.prevent="handleSubmit(submitForm)">
          <div class="dialog-form">
            <FormGroup rules="required" name="nameAr" errorName="NameAr">
              <template #default="{ attrs }">
                <InputText
                  v-bind="attrs"
                  v-model="form.nameAr"
                  focus
                  name="nameAr"
                  :label="$t('nameAr')"
                />
              </template>
            </FormGroup>
            <FormGroup rules="required" name="nameEn" errorName="NameEn">
              <template #default="{ attrs }">
                <InputText
                  v-bind="attrs"
                  v-model="form.nameEn"
                  name="nameEn"
                  :label="$t('nameEn')"
                />
              </template>
            </FormGroup>
            <FormGroup rules="required" name="airportCode" errorName="Code">
              <template #default="{ attrs }">
                <InputText
                  v-bind="attrs"
                  v-model="form.code"
                  name="code"
                  :label="$t('airportCode')"
                />
              </template>
            </FormGroup>
            <FormGroup rules="required" name="city" errorName="City">
              <template #default="{ attrs }">
                <InputAutoComplete
                  v-bind="attrs"
                  v-model="form.cityId"
                  :items="cities"
                  name="cityId"
                  :label="$t('city')"
                  item-text="name"
                  item-value="id"
                />
              </template>
            </FormGroup>
            <FormGroup rules="required" name="timezone" errorName="TimeZone">
              <template #default="{ attrs }">
                <InputText
                  v-bind="attrs"
                  v-model="form.timezone"
                  name="timeZone"
                  :label="$t('timeZone')"
                />
              </template>
            </FormGroup>
            <FormGroup rules="required" name="init" errorName="Init">
              <template #default="{ attrs }">
                <InputText
                  v-bind="attrs"
                  v-model="form.init"
                  name="init"
                  :label="$t('init')"
                />
                <a
                  href="https://www.nationsonline.org/oneworld/IATA_Codes/airport_code_list.htm"
                  target="_blank"
                  class="primary--text mt-3 d-inline-block font-weight-bold"
                >
                  <v-icon color="primary" class="mx-1">mdi-link</v-icon>
                  {{ $t("iata_codes") }}
                </a>
              </template>
            </FormGroup>
            <FormGroup rules="required" name="lat" errorName="Lat">
              <template #default="{ attrs }">
                <InputNumber
                  v-bind="attrs"
                  v-model="form.lat"
                  name="lat"
                  :label="$t('lat')"
                />
              </template>
            </FormGroup>
            <FormGroup rules="required" name="lon" errorName="Lon">
              <template #default="{ attrs }">
                <InputText
                  v-bind="attrs"
                  v-model="form.lon"
                  name="lon"
                  :label="$t('lon')"
                />
              </template>
            </FormGroup>
          </div>
          <div class="d-flex justify-end w-100 gap-md">
            <v-btn
              outlined
              color="primary"
              min-width="120"
              @click="dialog = false"
              >{{ $t("cancel") }}</v-btn
            >
            <v-btn
              class="primary"
              :loading="loading"
              type="submit"
              min-width="120"
              >{{ $t("save") }}</v-btn
            >
          </div>
        </v-form>
      </ValidationObserver>
    </CustomDialog>
  </section>
</template>


<script>
import form from "@/mixins/form";
export default {
  name: "CountryForm",
  mixins: [form],
  props: ["cities"],
  data() {
    return {
      form: {
        nameAr: "",
        nameEn: "",
        code: "",
        cityId: "",
        lat: "",
        lon: "",
        timezone: "",
        init: "",
      },
    };
  },
  methods: {},
};
</script>