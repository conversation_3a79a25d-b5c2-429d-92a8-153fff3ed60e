<template>
  <section>
    <v-btn min-width="20px" text v-if="id" @click="handleShow()">
      <v-icon color="primary"> mdi-pencil</v-icon>
    </v-btn>
    <v-btn @click="dialog = true" color="primary" depressed class="mb-4" v-else>
      {{ $t("new_country") }}
    </v-btn>
    <CustomDialog :dialog="dialog" @close-dialog="dialog = false">
      <template #header>
        <DialogHeader :title="$t('new_country')" />
      </template>
      <ValidationObserver ref="form" v-slot="{ handleSubmit }">
        <v-form @submit.prevent="handleSubmit(submitForm)">
          <div class="dialog-form">
            <FormGroup rules="required" name="nameAr" errorName="NameAr">
              <template #default="{ attrs }">
                <InputText
                  v-bind="attrs"
                  v-model="form.nameAr"
                  focus
                  name="nameAr"
                  :label="$t('nameAr')"
                />
              </template>
            </FormGroup>
            <FormGroup rules="required" name="nameEn" errorName="NameEn">
              <template #default="{ attrs }">
                <InputText
                  v-bind="attrs"
                  v-model="form.nameEn"
                  name="nameEn"
                  :label="$t('nameEn')"
                />
              </template>
            </FormGroup>
            <FormGroup
              rules="required"
              name="nationalityAr"
              errorName="NationalityAr"
            >
              <template #default="{ attrs }">
                <InputText
                  v-bind="attrs"
                  v-model="form.nationalityAr"
                  focus
                  name="nationalityAr"
                  :label="$t('nationalityAr')"
                />
              </template>
            </FormGroup>
            <FormGroup
              rules="required"
              name="nationalityEn"
              errorName="NationalityEn"
            >
              <template #default="{ attrs }">
                <InputText
                  v-bind="attrs"
                  v-model="form.nationalityEn"
                  name="nationalityEn"
                  :label="$t('nationalityEn')"
                />
              </template>
            </FormGroup>
            <FormGroup rules="required" name="countryCode" errorName="Code">
              <template #default="{ attrs }">
                <InputText
                  v-bind="attrs"
                  v-model="form.code"
                  name="code"
                  :label="$t('countryCode')"
                  hint="استخدم كود الدولة المكون من حرفين"
                />
                <a
                  href="https://www.iban.com/country-codes"
                  target="_blank"
                  class="primary--text mt-3 d-inline-block font-weight-bold"
                >
                  <v-icon color="primary" class="mx-1">mdi-link</v-icon>
                  {{ $t("country_codes") }}
                </a>
              </template>
            </FormGroup>
          </div>
          <div class="d-flex justify-end w-100 gap-md">
            <v-btn
              outlined
              color="primary"
              min-width="120"
              @click="dialog = false"
              >{{ $t("cancel") }}</v-btn
            >
            <v-btn
              class="primary"
              :loading="loading"
              type="submit"
              min-width="120"
              >{{ $t("save") }}</v-btn
            >
          </div>
        </v-form>
      </ValidationObserver>
    </CustomDialog>
  </section>
</template>


<script>
import form from "@/mixins/form";
export default {
  name: "CountryForm",
  mixins: [form],
  data() {
    return {
      form: {
        nameAr: "",
        nameEn: "",
        code: "",
        nationalityAr: "",
        nationalityEn: "",
      },
    };
  },
};
</script>