<template>
  <v-container class="mt-4">
    <v-card class="page-card">
      <v-card-title>
        <h4>{{ $t("footer_settings") }}</h4>
      </v-card-title>
      <v-card-text>
        <ValidationObserver ref="form" v-slot="{ handleSubmit }">
          <v-form
            @submit.prevent="handleSubmit(handleConfirm)"
            class="mt-4 px-4"
          >
            <v-row>
              <v-col cols="12" md="4">
                <FormGroup rules="required" name="nameAr" errorName="NameAr">
                  <template #default="{ attrs }">
                    <InputText
                      v-bind="attrs"
                      id="nameAr"
                      v-model="form.nameAr"
                      :label="$t('nameAr')"
                    />
                  </template>
                </FormGroup>
              </v-col>
              <v-col cols="12" md="4">
                <FormGroup rules="required" name="nameEn" errorName="NameEn">
                  <template #default="{ attrs }">
                    <InputText
                      v-bind="attrs"
                      id="nameEn"
                      v-model="form.nameEn"
                      :label="$t('nameEn')"
                    />
                  </template>
                </FormGroup>
              </v-col>
              <v-col cols="12" md="4">
                <FormGroup rules="required" name="aboutAr" errorName="AboutAr">
                  <template #default="{ attrs }">
                    <InputTextArea
                      v-bind="attrs"
                      id="aboutAr"
                      v-model="form.aboutAr"
                      :label="$t('aboutAr')"
                      :rows="3"
                    />
                  </template>
                </FormGroup>
              </v-col>
              <v-col cols="12" md="4">
                <FormGroup rules="required" name="aboutEn" errorName="AboutEn">
                  <template #default="{ attrs }">
                    <InputTextArea
                      v-bind="attrs"
                      id="aboutEn"
                      :rows="3"
                      v-model="form.aboutEn"
                      :label="$t('aboutEn')"
                    />
                  </template>
                </FormGroup>
              </v-col>
              <v-col cols="12" md="4">
                <FormGroup rules="required" name="address" errorName="Address">
                  <template #default="{ attrs }">
                    <InputTextArea
                      v-bind="attrs"
                      id="address"
                      :rows="3"
                      v-model="form.address"
                      :label="$t('address')"
                    />
                  </template>
                </FormGroup>
              </v-col>
            </v-row>

            <ExpansionPanels class="accordion">
              <ExpansionPanelsItem>
                <template #header>
                  <div>
                    <v-icon class="px-2">mdi-image</v-icon>
                    <span>{{ $t("logo") }}</span>
                  </div>
                </template>
                <template #content>
                  <v-row class="mt-4">
                    <v-col cols="12" md="4">
                      <ImageUpload
                        @add-image="addImage"
                        :imagePreview="form.imageUrl"
                        :seperatedLabel="$t('image')"
                        :hasDeleteOption="false"
                      />
                    </v-col>
                  </v-row>
                </template>
              </ExpansionPanelsItem>
              <ExpansionPanelsItem>
                <template #header>
                  <div>
                    <v-icon class="px-2">mdi-chat</v-icon>
                    <span>{{ $t("social_links") }}</span>
                  </div>
                </template>
                <template #content>
                  <v-row class="mt-4">
                    <v-col cols="12" md="4">
                      <FormGroup
                        rules="required"
                        name="email"
                        errorName="Email"
                      >
                        <template #default="{ attrs }">
                          <InputText
                            v-bind="attrs"
                            id="email"
                            v-model="form.email"
                            :label="$t('email')"
                          />
                        </template>
                      </FormGroup>
                    </v-col>
                    <v-col cols="12" md="4">
                      <FormGroup
                        rules="required"
                        name="whatsApp"
                        errorName="WhatsApp"
                      >
                        <template #default="{ attrs }">
                          <InputText
                            v-bind="attrs"
                            id="whatsApp"
                            v-model="form.whatsApp"
                            :label="$t('whatsApp')"
                          />
                        </template>
                      </FormGroup>
                    </v-col>
                    <v-col cols="12" md="4">
                      <FormGroup
                        rules="required"
                        name="phone"
                        errorName="Phone"
                      >
                        <template #default="{ attrs }">
                          <InputText
                            v-bind="attrs"
                            id="phone"
                            v-model="form.phone"
                            :label="$t('phone')"
                          />
                        </template>
                      </FormGroup>
                    </v-col>

                    <v-col cols="12" md="4">
                      <FormGroup
                        rules="required"
                        name="faceBook"
                        errorName="FaceBook"
                      >
                        <template #default="{ attrs }">
                          <InputText
                            v-bind="attrs"
                            id="faceBook"
                            v-model="form.faceBook"
                            :label="$t('faceBook')"
                          />
                        </template>
                      </FormGroup>
                    </v-col>
                    <v-col cols="12" md="4">
                      <FormGroup
                        rules="required"
                        name="twitter"
                        errorName="Twitter"
                      >
                        <template #default="{ attrs }">
                          <InputText
                            v-bind="attrs"
                            id="twitter"
                            v-model="form.twitter"
                            :label="$t('twitter')"
                          />
                        </template>
                      </FormGroup>
                    </v-col>
                    <v-col cols="12" md="4">
                      <FormGroup
                        rules="required"
                        name="insta"
                        errorName="Insta"
                      >
                        <template #default="{ attrs }">
                          <InputText
                            v-bind="attrs"
                            id="insta"
                            v-model="form.insta"
                            :label="$t('insta')"
                          />
                        </template>
                      </FormGroup>
                    </v-col>
                    <v-col cols="12" md="4">
                      <FormGroup rules="required" name="snap" errorName="Snap">
                        <template #default="{ attrs }">
                          <InputText
                            v-bind="attrs"
                            id="snap"
                            v-model="form.snap"
                            :label="$t('snap')"
                          />
                        </template>
                      </FormGroup>
                    </v-col>
                    <v-col cols="12" md="4">
                      <FormGroup
                        rules="required"
                        name="youtube"
                        errorName="Youtube"
                      >
                        <template #default="{ attrs }">
                          <InputText
                            v-bind="attrs"
                            id="youtube"
                            v-model="form.youtube"
                            :label="$t('youtube')"
                          />
                        </template>
                      </FormGroup>
                    </v-col>
                    <v-col cols="12" md="4">
                      <FormGroup
                        rules="required"
                        name="tiktok"
                        errorName="Tiktok"
                      >
                        <template #default="{ attrs }">
                          <InputText
                            v-bind="attrs"
                            id="tiktok"
                            v-model="form.tiktok"
                            :label="$t('tiktok')"
                          />
                        </template>
                      </FormGroup>
                    </v-col>
                    <v-col cols="12" md="4">
                      <FormGroup
                        rules="required"
                        name="linkedin"
                        errorName="Linkedin"
                      >
                        <template #default="{ attrs }">
                          <InputText
                            v-bind="attrs"
                            id="linkedin"
                            v-model="form.linkedin"
                            :label="$t('linkedin')"
                          />
                        </template>
                      </FormGroup>
                    </v-col>
                  </v-row>
                </template>
              </ExpansionPanelsItem>
              <ExpansionPanelsItem>
                <template #header>
                  <div>
                    <v-icon class="px-2">mdi-chat</v-icon>
                    <span>{{ $t("terms_and_privacy") }}</span>
                  </div>
                </template>
                <template #content>
                  <v-row class="mt-4">
                    <v-col cols="12" md="6">
                      <h5 class="primary--text mb-2">
                        {{ $t("terms_ar") }}
                      </h5>
                      <vue-editor
                        v-model="form.termsAr"
                        name="termsAr"
                        :editorToolbar="customToolbar"
                      />
                    </v-col>
                    <v-col cols="12" md="6">
                      <h5 class="primary--text mb-2">
                        {{ $t("terms_en") }}
                      </h5>
                      <vue-editor
                        v-model="form.termsEn"
                        name="termsEn"
                        :editorToolbar="customToolbar"
                      />
                    </v-col>
                    <v-col cols="12" md="6">
                      <h5 class="primary--text mb-2">
                        {{ $t("privacy_ar") }}
                      </h5>
                      <vue-editor
                        v-model="form.privacyAr"
                        name="privacyAr"
                        :editorToolbar="customToolbar"
                      />
                    </v-col>
                    <v-col cols="12" md="6">
                      <h5 class="primary--text mb-2">
                        {{ $t("privacy_en") }}
                      </h5>
                      <vue-editor
                        v-model="form.privacyEn"
                        name="privacyEn"
                        :editorToolbar="customToolbar"
                      />
                    </v-col>
                  </v-row>
                </template>
              </ExpansionPanelsItem>
            </ExpansionPanels>
          </v-form>
        </ValidationObserver>
      </v-card-text>
      <v-card-actions class="d-flex justify-end">
        <v-btn
          class="primary"
          min-width="150"
          @click="handleConfirm"
          :loading="loading"
          >{{ $t("save") }}</v-btn
        >
      </v-card-actions>
    </v-card>
  </v-container>
</template>

<script>
import { VueEditor } from "vue2-editor";

export default {
  name: "FooterRecord",
  components: {
    VueEditor,
  },
  data() {
    return {
      loading: false,
      imagePreview: "",
      form: {
        imageUrl: "",
        nameAr: "",
        nameEn: "",
        aboutAr: "",
        aboutEn: "",
        email: "",
        phone: "",
        faceBook: "",
        twitter: "",
        insta: "",
        snap: "",
        address: "",
        whatsApp: "",
        youtube: "",
        tiktok: "",
        termsAr: "",
        termsEn: "",
        privacyAr: "",
        privacyEn: "",
      },
      customToolbar: [
        ["bold", "italic", "underline", "strike", "link"],
        [{ align: "" }, { align: "justify" }, { align: "right" }],
        [{ list: "ordered" }, { list: "bullet" }, { list: "check" }],
        [{ direction: "rtl" }],
      ],
    };
  },
  methods: {
    addImage(event, imageData) {
      this.form.imageUrl = imageData.file;
      this.imagePreview = imageData.imageUrl;
      this.$http
        .post({
          reqName: "footer/add-logo",
          data: this.generateData(),
        })
        .then(() => {
          this.dialog = false;
          this.$emit("update-table");
        })
        .catch((error) => {
          console.log(error);
        });
    },
    getSettings() {
      this.$http
        .get({
          reqName: "footer/get",
        })
        .then(({ data: response }) => {
          this.form = response.data;
          this.form.imageUrl = response.data.logoUrl;
        });
    },
    handleConfirm() {
      this.loading = true;
      this.$http
        .put({
          reqName: "footer/update",
          data: this.generatePayload(),
        })
        .finally(() => {
          this.loading = false;
        });
    },
    generatePayload() {
      const formData = new FormData();
      for (const key in this.form) {
        formData.append(key, this.form[key]);
      }
      return formData;
    },
    generateData() {
      const data = new FormData();
      data.append("Image", this.form.imageUrl);

      return data;
    },
  },
  mounted() {
    this.getSettings();
  },
};
</script>