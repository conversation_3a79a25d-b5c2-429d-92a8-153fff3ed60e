<template>
  <section>
    <v-btn min-width="20px" text v-if="id" @click="handleShow()">
      <v-icon color="primary"> mdi-pencil</v-icon>
    </v-btn>
    <v-btn @click="dialog = true" color="primary" depressed class="mb-4" v-else>
      {{ $t("new_city") }}
    </v-btn>
    <CustomDialog :dialog="dialog" @close-dialog="dialog = false">
      <template #header>
        <DialogHeader :title="$t('new_city')" />
      </template>
      <ValidationObserver ref="form" v-slot="{ handleSubmit }">
        <v-form @submit.prevent="handleSubmit(submitForm)">
          <div class="dialog-form">
            <FormGroup rules="required" name="nameAr" errorName="NameAr">
              <template #default="{ attrs }">
                <InputText
                  v-bind="attrs"
                  v-model="form.nameAr"
                  focus
                  name="nameAr"
                  :label="$t('nameAr')"
                />
              </template>
            </FormGroup>
            <FormGroup rules="required" name="nameEn" errorName="NameEn">
              <template #default="{ attrs }">
                <InputText
                  v-bind="attrs"
                  v-model="form.nameEn"
                  name="nameEn"
                  :label="$t('nameEn')"
                />
              </template>
            </FormGroup>
            <FormGroup rules="required" name="cityCode" errorName="Code">
              <template #default="{ attrs }">
                <InputText
                  v-bind="attrs"
                  v-model="form.code"
                  name="code"
                  :label="$t('cityCode')"
                />
              </template>
            </FormGroup>
            <FormGroup rules="required" name="country" errorName="Code">
              <template #default="{ attrs }">
                <InputAutoComplete
                  v-bind="attrs"
                  v-model="form.countryId"
                  :items="countries"
                  name="countryId"
                  :label="$t('country')"
                  item-text="name"
                  item-value="id"
                />
              </template>
            </FormGroup>
          </div>
          <div class="d-flex justify-end w-100 gap-md">
            <v-btn
              outlined
              color="primary"
              min-width="120"
              @click="dialog = false"
              >{{ $t("cancel") }}</v-btn
            >
            <v-btn
              class="primary"
              type="submit"
              min-width="120"
              :loading="loading"
              >{{ $t("save") }}</v-btn
            >
          </div>
        </v-form>
      </ValidationObserver>
    </CustomDialog>
  </section>
</template>


<script>
import form from "@/mixins/form";
export default {
  name: "CountryForm",
  mixins: [form],
  props: ["countries"],
  data() {
    return {
      form: {
        nameAr: "",
        nameEn: "",
        code: "",
        countryId: "",
      },
    };
  },
  methods: {},
};
</script>