<template>
  <ValidationObserver ref="form" v-slot="{ handleSubmit }">
    <v-form @submit.prevent="handleSubmit(handleConfirm)" class="mt-4 px-4">
      <v-row>
        <v-col cols="12">
          <FormGroup
            rules="required"
            name="emailOrPhone"
            errorName="EmailOrPhone"
          >
            <template #default="{ attrs }">
              <InputText
                v-bind="attrs"
                id="emailOrPhone"
                v-model="form.emailOrPhone"
                focus
                :label="$t('emailOrPhone')"
              />
            </template>
          </FormGroup>
        </v-col>
      </v-row>
      <div class="d-flex justify-end buttons-container gap-md mt-4">
        <v-btn
          :loading="loading"
          color="primary"
          type="submit"
          min-width="120"
          class="create-btn"
          >{{ $t("confirm") }}</v-btn
        >
        <v-btn
          outlined
          color="primary"
          min-width="120"
          @click="$emit('close-dialog')"
          >{{ $t("cancel") }}</v-btn
        >
      </div>
    </v-form>
  </ValidationObserver>
</template>


<script>
export default {
  name: "AuthForgetPasswordEmailVerfication",
  data() {
    return {
      form: {
        emailOrPhone: null,
      },
      loading: false,
    };
  },

  methods: {
    handleConfirm() {
      this.$refs.form.validate().then((success) => {
        if (success && !this.serverErrors) {
          this.verifyEmail();
        }
      });
    },
    verifyEmail() {
      this.loading = true;
      const formData = new FormData();
      formData.append("EmailOrPhone", this.form.emailOrPhone);
      this.$http
        .post({
          reqName: "auth/request-resetting-password",
          data: formData,
        })
        .then(() => {
          // i want to commit mutation here
          this.$store.commit("SET_USERNAME", this.form.emailOrPhone);
          this.$emit("next-step");
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>