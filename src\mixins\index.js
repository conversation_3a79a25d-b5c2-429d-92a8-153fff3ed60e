import Vue from "vue";
import { mapGetters } from "vuex";

export const globalMixin = {
  computed: {
    ...mapGetters({
      pagination: "getPagination",
      user: "getUser",
      getQuery: "getQuery",
      getLocale: "getLocale",
      getPermissions: "getPermissions",
    }),
    serverErrors() {
      return this.$store.getters.serverErrors;
    },
    routeHashed() {
      if (this.$route.hash === "record") {
        return true;
      } else {
        return false;
      }
    },
    checkPermissionForCurrentRoute() {
      return this.checkPermissionManually(this.$route.meta.permissions);
    }

  },

  methods: {
    checkPermissionManually(permissionId) {

      let permissionCurrentRoute = this.getPermissions?.find(permission => {
        return permission.subModuleId === permissionId
      }) || null;

      let hasPermission = {
        show: false,
        insert: false,
        update: false,
        delete: false,
      };
      if (!permissionCurrentRoute) return hasPermission;
     
      Object.entries(permissionCurrentRoute).forEach(([key, value]) => {
        let subPermissionName = key.split("_")[1];
       
        if (subPermissionName === 'Insert' || subPermissionName === 'Update' || subPermissionName === 'Delete' || subPermissionName === 'Show') {
          subPermissionName = subPermissionName.toLowerCase();
          hasPermission[subPermissionName] = value;
        }
      })
      return hasPermission;
    },
    handleDebounce(callback) {
      if (this.timStamp) {
        clearTimeout(this.timStamp);
      }
      this.timStamp = setTimeout(() => {
        callback();
      }, 500);
    },

    popUp(
      title = this.$t("message.warning"),
      type = "question",
      singleButton = false
    ) {
      const swal = this.$swal.fire({
        title,
        type,
        showCloseButton: false,
        showCancelButton: !singleButton,
        showConfirmButton: true,
        confirmButtonText: `${this.$t("confirm")}`,
        cancelButtonText: `${this.$t("cancel")}`
      });
      return swal;
    },
    convertDate(date, format = "LL", locale = this.$i18n.locale) {
      return this.$moment(date).locale(locale).format(format);
    }
  },
  filters: {
    truncate(value, count) {
      if (value && value.length > count) {
        return value && value.substring(0, count) + "...";
      } else {
        return value;
      }
    }
  }
};

Vue.mixin(globalMixin);
