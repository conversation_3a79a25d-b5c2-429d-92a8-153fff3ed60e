<template>
  <div>
    <Loader />
    <SideBar />
    <router-view />
  </div>
</template>

<script>
import { mapActions } from "vuex";
// import { getMessaging, getToken, onMessage } from "firebase/messaging";
// import { messaging } from "@/plugins/firebase.js";
export default {
  name: "DefaultLayout",
  components: {
    SideBar: () => import("@/components/SideBar"),
  },
  methods: {
    // async checkPermissionAndRequestToken() {
    //   if (!Notification.permission === "granted") {
    //     const permission = await Notification.requestPermission();
    //     if (permission === "granted") {
    //       this.getIdToken();
    //     }
    //   }
    // },
    // async getIdToken() {
    //   try {
    //     const token = await getToken(messaging);
    //     if (token) {
    //       this.authenticateToken(token);
    //     }
    //   } catch (e) {
    //     console.error("Error:", e);
    //   }
    // },

    // async handleIncomingMessage() {
    //   console.log("Handling background message...");
    //   await onMessage(messaging, (payload) => {
    //     console.log("Message received. ", payload);
    //     this.getNotifications();
    //   });
    // },

    // async startListening() {
    //   await this.handleIncomingMessage();
    //   // await this.checkPermissionAndRequestToken();
    // },

    // authenticateToken(token) {
    //   const formData = new FormData();
    //   formData.append("DeviceToken", token);
    //   this.$http.post({
    //     reqName: "user-device/create",
    //     data: formData,
    //   });
    // },
  },
};
</script>
