<template>
  <div>
    <ValidationObserver ref="form" v-slot="{ handleSubmit }">
      <v-form @submit.prevent="handleSubmit(handleConfirm)" class="mt-4 px-4">
        <v-row justify="center">
          <v-col cols="12" md="9">
            <InputOtp
              length="6"
              type="password"
              v-model="form.otp"
              @finish="handleConfirm"
              class="otp-card__input"
              :loading="loading"
            />
          </v-col>
        </v-row>
        <div class="d-flex justify-end buttons-container gap-md mt-4">
          <v-btn
            :loading="loading"
            color="primary"
            type="submit"
            min-width="120"
            class="create-btn"
            >{{ $t("confirm") }}</v-btn
          >
          <v-btn
            outlined
            color="primary"
            min-width="120"
            @click="$emit('close-dialog')"
            >{{ $t("cancel") }}</v-btn
          >
        </div>
      </v-form>
    </ValidationObserver>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "OtpVerification",
  data() {
    return {
      form: {
        otp: "",
      },
      loading: false,
    };
  },
  computed: {
    ...mapGetters({
      getUserName: "getUserName",
    }),
  },
  methods: {
    handleConfirm() {
      this.$refs.form.validate().then((success) => {
        if (success && !this.serverErrors) {
          this.verifyOtp();
        }
      });
    },
    generateData() {
      const formData = new FormData();
      formData.append("EmailOrPhone", this.getUserName);
      formData.append("Otp", this.form.otp);
      return formData;
    },
    verifyOtp() {
      this.loading = true;
      this.$http
        .post({
          reqName: "auth/verify-resetting-password",
          data: this.generateData(),
        })
        .then(({ data: response }) => {
          this.$store.commit("SET_RESET_TOKEN", response.data.accessToken);
          this.$emit("next-step");
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>