<template>
  <v-menu rounded offset-y bottom transition="slide-x-transition">
    <template #activator="{ attrs, on }">
      <v-badge
        color="#ff7272"
        :content="unreadNotifications"
        offset-x="22"
        offset-y="22"
      >
        <v-btn icon v-bind="attrs" v-on="on">
          <v-icon size="24">mdi-bell-outline</v-icon>
        </v-btn>
      </v-badge>
    </template>

    <!-- dropdown card -->
    <v-card>
      <v-list three-line dense max-width="400" min-width="400">
        <v-subheader class="pa-2 font-weight-bold">{{
          $t("notififcations")
        }}</v-subheader>
        <div v-for="(item, index) in notifications" :key="index">
          <v-divider
            v-if="index > 0 && index < notifications.length"
            inset
          ></v-divider>

          <v-list-item
            :class="{ 'notifications-read': !item.seenAt }"
            @click="goNotification(item)"
          >
            <v-list-item-avatar size="32" color="primary">
              <v-icon dark small>mdi-email-outline</v-icon>
            </v-list-item-avatar>

            <v-list-item-content>
              <v-list-item-title v-text="item.title"></v-list-item-title>
              <v-list-item-subtitle
                class="caption"
                v-text="item.message"
              ></v-list-item-subtitle>
            </v-list-item-content>
            <v-list-item-action class="align-self-center">
              <v-list-item-action-text
                >{{ convertDate(item.createdAt, "DD-MM-YYYY HH:mm") }}
                <br />
              </v-list-item-action-text>
            </v-list-item-action>
          </v-list-item>
        </div>
      </v-list>

      <div class="text-center py-2">
        <v-btn small color="primary" to="/notifications">{{
          $t("showAll")
        }}</v-btn>
      </div>
    </v-card>
  </v-menu>
</template>


<script>
import { mapGetters, mapActions } from "vuex";
export default {
  name: "Notifications",

  computed: {
    ...mapGetters({
      allNotifications: "getNotifications",
      getRecentNotifications: "getRecentNotifications",
      unreadNotifications: "getUnreadCount",
    }),
    notifications() {
      return this.getRecentNotifications.slice(0, 5);
    },
  },
  methods: {
    ...mapActions({
      getUnSeenNotifications: "getUnSeenNotifications",
    }),
    goNotification(notification) {
      const { subModuleId, documentNo } = notification;
      const routes = {
        2: `/orders/${documentNo}/order-details`,
        21: `/sales-invoice/${documentNo}/show`,
        22: `/purchases-invoice/${documentNo}/show`,
        23: `/expired-invoices/${documentNo}/show`,
        24: `/return-invoice/${documentNo}/show`,
        18: `/customerReview`,
      };

      if (routes[subModuleId]) {
        this.$router.push(routes[subModuleId]);
      }
    },

  },
  // mounted() {
  //   this.getUnSeenNotifications();
  // },
};
</script>


<style lang="scss">
@import "index.scss";
</style>