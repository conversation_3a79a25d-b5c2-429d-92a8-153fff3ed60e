<template>
  <v-card>
    <v-card-text>
      <CustomTable
        v-if="rendering"
        :tableHeaders="headers"
        :itemsData="payments"
        :fromStore="false"
        :showPagination="false"
      >
      </CustomTable>
    </v-card-text>
  </v-card>
</template>


<script>
export default {
  props: {
    payments: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      rendering: true,
    };
  },
  computed: {
    headers() {
      const headers = [
        {
          name: "#",
          value: "referenceNumber",
          showColumn: true,
        },
        {
          name: "number",
          value: "number",
          showColumn: true,
        },
        {
          name: "reservationId",
          value: "reservationId",
          showColumn: true,
        },
        {
          name: "name",
          value: "name",
          sortable: false,
          showColumn: true,
        },
        {
          name: "description",
          value: "description",
          sortable: false,
          showColumn: true,
        },
        {
          name: "gateway",
          value: "gatewayId",
          sortable: false,
          showColumn: true,
        },
        {
          name: "status",
          value: "status",
          sortable: false,
          showColumn: true,
        },
        {
          name: "amount",
          value: "amount",
          sortable: false,
          showColumn: true,
        },

        {
          name: "fee",
          value: "fee",
          sortable: false,
          showColumn: true,
        },
      ];
      return headers.filter((head) => head.showColumn);
    },
  },
};
</script>