import axios from "axios";
import store from "@/store";
import { baseURL } from "@/config/baseUrl";

import Vue from "vue";
import { handleSuccessResponse, handleErrorResponse, configureRequestHeaders } from './response';


// create an axios instance
const service = axios.create({
  timeout: 60000, // request timeout
  baseURL: "https://takeed.runasp.net/api/v1",
});

// request interceptor
service.interceptors.request.use(
  async (config) => {
    configureRequestHeaders(config, store)
    return config;
  },
  (error) => {
    // Do something with request error

    return Promise.reject(error);
  }
);

service.interceptors.response.use(

  (response) => {
    store.dispatch("ClearServerErrors");
    handleSuccessResponse(response, store);
    return response;
  },
  (error) => {
    handleErrorResponse(error);
    return Promise.reject(error);
  }
);


export default service;
