import { $http } from "@/api";

export default {
  state: () => ({
    notifications: [],
    pagination: {
      currentPage: 1,
      totalPages: 0,
      pageSize: 0,
      totalCount: 0,
    },
    recentNotifications: [],
    unreadCount: 0
  }),
  mutations: {
    SET_NOTIFICATIONS(state, payload) {
      state.notifications = payload
    }, RESET_PAGINATION(state) {
      state.pagination = {
        currentPage: 1,
        totalPages: 0,
        pageSize: 0,
        totalCount: 0,
      };
    },
    SET_PAGINATION(state, payload) {
      state.pagination = {
        ...state.pagination,
        ...payload
      }
    },
    SET_RECENT_NOTIFICATIONS(state, payload) {
      state.recentNotifications = payload
    },
    SET_UNREAD_COUNT(state, payload) {
      state.unreadCount = payload
    }
  },
  actions: {
    getNotification({ commit, rootState }) {
      return $http.get({
        reqName: "notifications/branch",
        query: {
          ...rootState.query.query
        },
      }).then(({ data: response }) => {
        commit("SET_NOTIFICATIONS", response.data)
        if (response.meta) {
          commit("SET_PAGINATION", response.meta)
        }
      })
    },
    getUnSeenNotifications({ commit }) {
      return $http
        .get({
          reqName: "notifications/branch?GetSeeOnly=false",
        }).then(({ data: response }) => {
          commit("SET_RECENT_NOTIFICATIONS", response.data)
          commit("SET_UNREAD_COUNT", response.meta.totalCount)
        })
    }

  },
  getters: {
    getNotifications(state) {
      return state.notifications
    },
    getNotificationsPagination(state) {
      return state.pagination
    },
    getRecentNotifications(state) {
      return state.recentNotifications
    },
    getUnreadCount(state) {
      return state.unreadCount
    }
  }
}