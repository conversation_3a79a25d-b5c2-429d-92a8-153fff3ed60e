<template>
  <v-stepper v-model="stepper">
    <v-stepper-header>
      <template v-for="n in steps">
        <v-stepper-step :key="`step-${n}`" :step="n"> </v-stepper-step>
        <v-divider v-if="n !== steps" :key="n"></v-divider>
      </template>
    </v-stepper-header>
    <v-stepper-content v-for="n in steps" :key="n" :step="n">
      <slot :name="`step-${n}`" />
    </v-stepper-content>
  </v-stepper>
</template>

<script>
export default {
  props: {
    stepper: {
      type: Number,
      default: 1,
    },
    steps: {
      type: Number,
      default: 3,
    },
  },
};
</script>

<style lang="scss">
@import "index.scss";
</style>