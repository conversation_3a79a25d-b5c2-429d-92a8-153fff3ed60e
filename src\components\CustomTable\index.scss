@import "@/assets/scss/mixins";
.table{
  border-radius: 16px;
  background-color: transparent !important;

  thead{
    border-bottom-color:#ecf3fa ;
    th {
      text-transform: capitalize;
      white-space: nowrap;
      
      text-align: center;
      min-width: 150px;
      padding: 15px !important;
      font-weight: bold;
    }

    // tr {
    //   & th:first-child{
    //     border-bottom-right-radius:9px;
    //     border-top-right-radius:9px;
    //   }
    // }
  }
  tbody{
    tr{
      background-color: transparent !important;
      td{
        text-align: center;
          padding: 12px 15px !important;
          border: none !important;
          // max-width: 200px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          
        // &:first-child{
        //   border-top-right-radius: 9px;
        //   border-bottom-right-radius: 9px;
        // }
      }
  
    }
  }



    scrollbar-width: thin;
    // make scroll bar width 100px
    ::-webkit-scrollbar {
         width: 100px;
        height: 6px
    }
  
    ::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px grey;
      border-radius: 40px;
    }
    ::-webkit-scrollbar-thumb {
      background: rgba(#0da487,  0.6);
      border-radius:40px;
    }
    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
        background: var(--primary);
    }
  
}

.theme--light{
.table {
    td{
    color: #4a5568 !important;
  }
  thead tr{
    background-color: #f3f3f3 !important;
  }
  tbody {
    tr{
      &:nth-child(even):not(.v-data-table__empty-wrapper) {
        background-color: #f8f8f8 !important;
      }
    }
  }
  
}
}