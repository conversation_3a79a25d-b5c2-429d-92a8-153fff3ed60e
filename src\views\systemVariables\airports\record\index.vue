<template>
  <main class="mt-4">
    <v-container>
      <v-card class="page-card">
        <v-card-text>
          <v-row>
            <v-col cols="12">
              <CustomTable
                v-if="rendering"
                :tableHeaders="headers"
                :reqName="`${url}/paginated?GetActiveOnly=false`"
                :slotColumnNames="['#', 'isActive', 'actions']"
                :title="$t('airports')"
              >
                <template #top-right>
                  <AirportForm :url="url" :cities="cities" />
                </template>
                <template #top-left>
                  <InputText
                    v-model="search"
                    name="search"
                    hide-details
                    outlined
                    class="mx-4"
                    dense
                    clearable
                    :label="$t('search')"
                    @input="setSearchQuery"
                  >
                  </InputText>
                </template>
                <template #item.#="{ item, index }">
                  {{ index + pagination.from }}
                </template>
                <template #item.isActive="{ item }">
                  <toggle-service
                    :is-edit="true"
                    :model-name="`${url}/activate-deactivate`"
                    :model-id="item.id"
                    v-model="item.isActive"
                  />
                </template>
                <template #item.actions="{ item }">
                  <td class="d-flex align-center justify-center">
                    <AirportForm :url="url" :id="item.id" :cities="cities" />
                    <ImageDialog
                      :image="item.image"
                      url="airport/add-image"
                      deleteUrl="airport/delete-image"
                      :hasDeleteOption="true"
                      :id="item.id"
                      @update-table="updateTable"
                    />
                  </td>
                </template>
              </CustomTable>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-container>
  </main>
</template>



<script>
import AirportForm from "@/components/Forms/Airport";
import { mapActions } from "vuex";

export default {
  name: "AirportsRecord",
  components: {
    AirportForm,
  },
  data() {
    return {
      rendering: true,
      url: "airport",
      search: "",
      cities: [],
    };
  },
  computed: {
    headers() {
      const headers = [
        {
          name: "#",
          value: "#",
          showColumn: true,
        },
        {
          name: "name",
          value: "name",
          sortable: false,
          showColumn: true,
        },
        {
          name: "init",
          value: "init",
          sortable: false,
          showColumn: true,
        },

        {
          name: "active",
          value: "isActive",
          sortable: false,
          showColumn: true,
        },

        {
          name: "actions",
          value: "actions",
          showColumn: true,
        },
      ];
      return headers.filter((head) => head.showColumn);
    },
  },
  methods: {
    ...mapActions(["setQuery"]),
    setSearchQuery() {
      console.log(this.search);
      let query = {
        SearchString: this.search,
      };
      this.setQuery(query);
      // this.rendering = false;
      // setTimeout(() => {
      //   this.rendering = true;
      // }, 10);
    },
    getCities() {
      this.$http
        .get({
          reqName: "city/paginated?pageSize=1000",
        })
        .then(({ data: response }) => {
          this.cities = response.data;
        });
    },
  },
  mounted() {
    this.getCities();
  },
};
</script>