<template>
  <div class="horizontal-tabs">
    <v-tabs v-model="tab" slider-color="primary" show-arrows>
      <v-tab v-for="(item, i) in tabs" :key="i" :href="`#tab-${i}`">
        {{ item.title }}
      </v-tab>
    </v-tabs>

    <v-tabs-items v-model="tab" class="py-3 transparent">
      <slot />
    </v-tabs-items>
  </div>
</template>

<script>

export default {
  name: "TabsHorizontal",
  props: {
    tabs: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      tab: null,
    };
  },
};
</script>

<style lang="scss">
.horizontal-tabs {
  .v-tabs {
    margin-bottom: 10px;
  }
  .v-tab.v-tab--active {
    background-color: unset !important;
    color: #000 !important;
    font-weight: 900;
  }
}
</style>
