<template>
  <v-row class="d-flex align-center my-0">
    <v-col cols="2" md="2" lg="3">
      <div class="travel--title d-flex align-center">
        <img src="https://tjwlcdn.com/img/air/SV.png" alt="logo" />
        <div class="hidden-md-and-down size-12">السعودية</div>
      </div>
    </v-col>
    <v-col cols="3" sm="2" md="3" lg="2">
      <div class="travel--departure__time">
        <div class="d-flex align-center">
          {{ convertDate(travel?.departure_at, "hh:mm", "en") }}
          <span class="size-12 text-uppercase hidden-md-and-down mx-1">
            {{ convertDate(travel?.departure_at, "A") }}
          </span>
        </div>
      </div>

      <div class="travel--departure__tooltip">
        <span class="text-uppercase size-14">{{
          travel?.departure_iataCode
        }}</span>
      </div>
    </v-col>
    <v-col cols="4" sm="6" md="4" lg="5" class="travel--stop">
      <svg
        focusable="false"
        color="inherit"
        fill="currentcolor"
        aria-hidden="true"
        role="presentation"
        viewBox="0 0 16 16"
        preserveAspectRatio="xMidYMid meet"
        width="24px"
        height="24px"
        class="travel--stop__icon"
      >
        <g fill="none" fill-rule="evenodd">
          <path d="M0 0h16v16H0z"></path>
          <path
            fill="currentcolor"
            d="M8.002 13.429h.42c.557 0 1.009-.44 1.009-.572v.572c0-.316-.44-.572-1.003-.572h-.14l1.714-1.714h.14c.554 0 1.003-.439 1.003-.572v.572c0-.316-.449-.572-.857-.572l1.143-1.428c0-.286.857-.288.857-.288s1.43.028 2.287.002C15.432 8.883 16 8.286 16 8c.003-.286-.568-.857-1.425-.857h-2.287s-.857 0-.857-.286L10.288 5.43c.012 0 0 0 0 0 .473 0 .857-.44.857-.572v.572c0-.316-.438-.572-1.003-.572h-.14L8.287 3.143h.14c.555 0 1.004-.439 1.004-.572v.572c0-.316-.444-.572-.992-.572h-.46L6.253.534S5.895 0 5.39 0l-.441.029s.482.828 1.625 3.4C7.716 6 7.716 6 7.716 6.857l-.572.286H4.572c0 .055-.285 0-1.428.286L1.453 5.547S.857 4.857 0 4.857l1.429 2.857L0 8l1.429.286L0 11.143c.857 0 1.363-.44 1.363-.44l1.78-2.132c1.144.286 1.43.286 1.43.286h2.571l.572.286c0 .857 0 .857-1.143 3.428C5.43 15.143 4.977 16 4.977 16h.412s.516-.108.863-.506l1.75-2.065z"
          ></path>
        </g>
      </svg>
      <div class="travel--stop__tooltip">
        <div
          class="travel--stop__tooltip--activator"
          v-if="travel?.numberOfStops > 0"
        >
          <span class="size-12">{{ $t("stop") }}</span>
        </div>

        <div v-else class="travel--stop__tooltip--activator green--text">
          <span>{{ $t("direct") }}</span>
        </div>
      </div>
      <!-- <div class="travel--stop__detailes justify-center">
        <Tooltip bottom :text="$t('total_duration')">
          <div
            class="d-flex align-center justify-space-between travel--stop__detailes--duration"
          >
            <span
              v-html="timeIcon"
              class="d-flex align-center justify-center"
            ></span>
            <span
              class="size-12 travel--stop__detailes--duration__text"
              style="direction: ltr"
            >
              {{ getDuration(travel?.duration).hours() }}h
              {{ getDuration(travel?.duration).minutes() }}m
            </span>
          </div>
        </Tooltip>
      </div> -->
    </v-col>
    <v-col cols="3" sm="2" md="3" lg="2" class="travel--arrival">
      <div class="travel--arrival__time">
        <div v-if="!isMobile" class="d-flex align-center">
          {{ convertDate(travel.arrival_at, "hh:mm", "en") }}
          <span class="size-12 text-uppercase hidden-md-and-down mx-1">
            {{ convertDate(travel.arrival_at, "A") }}
          </span>
        </div>
        <div v-else>
          {{ convertDate(travel.arrival_at, "HH:mm", "en") }}
        </div>
      </div>

      <div class="travel--arrival__tooltip">
        <!-- <Tooltip :text="arrivalTooltip" top> -->
        <span>{{ travel.arrival_iataCode }}</span>
      </div>
      <div class="travel--arrival__count hidden-sm-and-down">
        <ToolTip
          top
          :text="
            $t('arrival_day') +
            ' ' +
            convertDate(travel.arrival_at, 'dddd D MMMM')
          "
        >
          <span class="size-14">+1</span>
        </ToolTip>
      </div>
    </v-col>
  </v-row>
  <!-- <v-row class="my-0">
    <v-col cols="12" class="pt-0">
      <div style="color: #85979e" class="size-12">
        <span> السعودية | </span>
        <span>TK-915</span>
      </div>
    </v-col>
  </v-row> -->
  <!-- </section> -->
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "Travel",
  props: {
    travel: {
      type: Object,
      default: () => {},
    },
    isMobile: {
      type: Boolean,
      default: false,
    },
    checkedBags: {
      type: Boolean,
      default: false,
    },
    baggageOptions: {
      type: Object,
      default: () => {},
    },
    quantity: {
      type: String,
      default: "",
    },
  },
  computed: {
    ...mapGetters({
      loading: "flights/getLoading",
      locations: "flights/getLocations",
    }),

    timeIcon() {
      return `<svg
              focusable="false"
              color="gray400"
              fill="currentcolor"
              aria-hidden="true"
              role="presentation"
              viewBox="0 0 16 15"
              preserveAspectRatio="xMidYMid meet"
              size="16"
              width="16"
              height="16"
              class="travel--stop__detailes--duration__icon"
            >
              <path
                fill="#85979E"
                fill-rule="evenodd"
                d="M8 13.7c-3.42 0-6.2-2.78-6.2-6.2 0-3.42 2.78-6.2 6.2-6.2 3.42 0 6.2 2.78 6.2 6.2 0 3.42-2.78 6.2-6.2 6.2zm0 .8c-3.862 0-7-3.138-7-7s3.138-7 7-7 7 3.138 7 7-3.138 7-7 7zm-.353-7.847A.917.917 0 108.84 7.87l1.369-.008a.356.356 0 00.362-.362.363.363 0 00-.362-.362l-1.362.009a.92.92 0 00-.475-.485l-.01-2.48A.356.356 0 008 3.818a.356.356 0 00-.362.362l.009 2.472z"
                clip-rule="evenodd"
              ></path>
              <path
                fill="#85979E"
                d="M7.647 6.653l.019.046.03-.012v-.034h-.05zM8.84 7.87v-.05h-.033l-.013.03.046.02zm1.369-.008v-.05.05zm0-.724v-.05.05zm-1.362.009l-.046.02.013.03h.033v-.05zm-.475-.485h-.05v.032l.03.013.02-.045zm-.01-2.48h-.05.05zm-.724 0h-.05.05zM1.75 7.5A6.255 6.255 0 008 13.75v-.1A6.155 6.155 0 011.85 7.5h-.1zM8 1.25A6.255 6.255 0 001.75 7.5h.1A6.155 6.155 0 018 1.35v-.1zm6.25 6.25A6.255 6.255 0 008 1.25v.1a6.155 6.155 0 016.15 6.15h.1zM8 13.75a6.255 6.255 0 006.25-6.25h-.1A6.155 6.155 0 018 13.65v.1zM.95 7.5c0 3.89 3.16 7.05 7.05 7.05v-.1A6.955 6.955 0 011.05 7.5h-.1zM8 .45C4.11.45.95 3.61.95 7.5h.1A6.955 6.955 0 018 .55v-.1zm7.05 7.05C15.05 3.61 11.89.45 8 .45v.1a6.955 6.955 0 016.95 6.95h.1zM8 14.55c3.89 0 7.05-3.16 7.05-7.05h-.1A6.955 6.955 0 018 14.45v.1zm-.372-7.943a.967.967 0 00-.595.893h.1c0-.36.22-.67.533-.8l-.038-.093zm-.595.893c0 .535.432.967.967.967v-.1a.867.867 0 01-.867-.867h-.1zM8 8.467a.967.967 0 00.886-.577l-.092-.04A.867.867 0 018 8.367v.1zm.84-.547l1.369-.008v-.1l-1.37.008.001.1zm1.369-.008c.232 0 .412-.18.412-.412h-.1a.306.306 0 01-.313.312v.1zm.412-.412a.413.413 0 00-.412-.412v.1c.166 0 .312.136.312.312h.1zm-.413-.412l-1.362.009.001.1 1.362-.009v-.1zm-1.315.04a.97.97 0 00-.5-.512l-.041.091a.87.87 0 01.449.46l.092-.04zm-.471-.467l-.01-2.48h-.1l.01 2.48h.1zm-.01-2.48A.406.406 0 008 3.77v.1c.177 0 .312.135.312.312h.1zM8 3.77a.406.406 0 00-.412.412h.1c0-.177.134-.312.312-.312v-.1zm-.412.412l.009 2.473h.1l-.01-2.473h-.1z"
              ></path>
            </svg>`;
    },
    bagIcon() {
      return this.checkedBags ? "$bagIcon" : "$noBagIcon";
    },
    checkedIcon() {
      return `    <svg
                  focusable="false"
                  color="inherit"
                  fill="currentcolor"
                  aria-hidden="true"
                  role="presentation"
                  viewBox="0 0 15 16"
                  preserveAspectRatio="xMidYMid meet"
                  size="18"
                  width="18"
                  height="18"
                  class="sc-gacfCG jRIHJS sc-hMqMXs ddpIXY"
                >
                  <path
                    d="M7.5.7C3.364.7 0 4.064 0 8.2c0 4.135 3.364 7.5 7.5 7.5S15 12.335 15 8.2C15 4.064 11.636.7 7.5.7z"
                  ></path>
                  <path
                    fill="#319E37"
                    stroke="#319E37"
                    stroke-width="0.6"
                    d="M10.452 6.972l-3.32 3.318a.51.51 0 01-.721 0L4.75 8.631a.51.51 0 11.722-.722l1.299 1.299L9.73 6.25a.51.51 0 11.722.722z"
                  ></path>
                </svg>`;
    },
    noCheckedIcon() {
      return `<svg focusable="false" color="primary" fill="currentcolor" aria-hidden="true" role="presentation" viewBox="0 0 15 15" preserveAspectRatio="xMidYMid meet" size="18" width="18" height="18" class="sc-hMqMXs gTWhfA"><path fill="#FCE7E9" d="M7.5 15a7.5 7.5 0 100-15 7.5 7.5 0 000 15z"></path><rect width="1.67" height="7.307" x="4.4" y="5.481" fill="#E24C4B" rx="0.835" transform="rotate(-45 4.4 5.48)"></rect><rect width="1.67" height="7.307" x="5.581" y="10.7" fill="#E24C4B" rx="0.835" transform="rotate(-135 5.58 10.7)"></rect></svg>`;
    },
    waitingText() {
      // const departureSegment = this.travel?.segments[0]?.arrival.at;
      // const arrivalSegment = this.travel?.segments[1]?.departure;
      // const arrivalTime = this.$moment(arrivalSegment);
      // const departureTime = this.$moment(departureSegment);
      // const waitingTime = this.$moment.duration(
      //   arrivalTime.diff(departureTime)
      // );
      // const stopLocation = this.locations.find(
      //   (location) => location.code === arrivalSegment.iataCode
      // );
      // if (stopLocation) {
      //   const formattedWaitingTime = `${this.$t(
      //     "waiting_time"
      //   )} ${waitingTime.hours()}h ${waitingTime.minutes()}m ${this.$t("in")} ${
      //     stopLocation.cityName
      //   }`;
      //   return formattedWaitingTime;
      // }
      return null;
    },
    // departureTooltip() {
    //   const hasLocations = this.locations.length > 0;
    //   if (hasLocations) {
    //     const departureFlight = this.locations.find(
    //       (location) => location.code === this.travel?.departure_iataCode
    //     );
    //     if (departureFlight) {
    //       const { code, cityName, countryName, name } = departureFlight;
    //       return `${code} ${cityName}, ${countryName} - ${name}`;
    //     }
    //   }
    // },
    // arrivalTooltip() {
    //   if (this.locations.length > 0) {
    //     const arrival = this.locations.find(
    //       (loc) => loc.code === this.travel.arrival_iataCode
    //     );
    //     if (arrival) {
    //       return `${arrival.code} ${arrival.cityName},${arrival.countryName} - ${arrival.name}`;
    //     }
    //   }
    //   return null; // Return null if no valid arrival is found
    // },
    baggageText() {
      const weight = this.baggageOptions?.weight;
      const weightUnit = this.baggageOptions?.weightUnit;
      return `${this.$t("baggage_weight")} ${weight} ${this.$t(weightUnit)}`;
    },
  },
};
</script>
