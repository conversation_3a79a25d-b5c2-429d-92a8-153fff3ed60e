.accordion{  
  gap: 24px;
  .v-expansion-panel{
    border-radius: 16px !important;

    border: 2px solid rgba(#ddd, 50%);

   &::before {
      box-shadow: none !important;
  }
  &::after {
      border: none !important;
  }
  button{
    min-height: initial !important;
    &:focus {
        &::before {
            opacity: 0 !important;
        }
    }
  }

  &-header{
    padding: 20px;
    border-radius: 16px;
    .v-icon:hover{
      color:#fff !important;
    }
    .v-icon{
      color:#f5f5f5 !important;
    }
  }
  }
  &--content{
    color: #454F5B;
  }

}