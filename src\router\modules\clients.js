export const clientsRoutes = {
  path: "/clients",
  to: "/clients",
  icon: "mdi-account-multiple",
  activeRoutes: ["clients_record"],
  meta: {
    title: "clients",
  },
  showInMenu: true,
  order: 6,
  component: () => import("@/views/clients"),
  children: [
    {
      path: "/",
      component: () => import("@/views/clients/record"),
      name: "clients_record",
      icon: "mdi-file-document-multiple-outline",
      to: "/clients",
      allowed: false,
      meta: {
        title: "clients_record",

      }
    },
    {
      path: "/clients/:userID",
      name: "clients_reservations",
      component: () => import("@/views/clients/reservationDetails"),
    },
  ]
};
