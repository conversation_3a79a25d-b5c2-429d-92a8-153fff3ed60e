<template>
  <section>
    <v-btn min-width="20px" text v-if="id" @click="handleShow()">
      <v-icon color="primary"> mdi-pencil</v-icon>
    </v-btn>
    <v-btn @click="dialog = true" color="primary" depressed class="mb-4" v-else>
      {{ $t("new_carousel") }}
    </v-btn>
    <CustomDialog :dialog="dialog" @close-dialog="dialog = false">
      <template #header>
        <DialogHeader :title="$t('new_carousel')" />
      </template>
      <ValidationObserver ref="form" v-slot="{ handleSubmit }">
        <v-form @submit.prevent="handleSubmit(submitForm)">
          <div class="dialog-form">
            <FormGroup rules="required" name="nameAr" errorName="NameAr">
              <template #default="{ attrs }">
                <InputText
                  v-bind="attrs"
                  v-model="form.nameAr"
                  focus
                  name="nameAr"
                  :label="$t('nameAr')"
                />
              </template>
            </FormGroup>
            <FormGroup rules="required" name="nameEn" errorName="NameEn">
              <template #default="{ attrs }">
                <InputText
                  v-bind="attrs"
                  v-model="form.nameEn"
                  name="nameEn"
                  :label="$t('nameEn')"
                />
              </template>
            </FormGroup>
            <FormGroup
              rules="required"
              name="orderPriority"
              errorName="OrderPriority"
            >
              <template #default="{ attrs }">
                <InputNumber
                  v-bind="attrs"
                  v-model="form.orderPriority"
                  name="orderPriority"
                  :label="$t('orderPriority')"
                />
              </template>
            </FormGroup>
            <InputUpload
              @fileSelected="handleUpdatePhoto"
              :imagePreview="form.image"
              id="image"
              :label="$t('image')"
            />
            <FormGroup
              rules="required"
              name="description"
              errorName="Description"
            >
              <template #default="{ attrs }">
                <InputTextArea
                  v-bind="attrs"
                  v-model="form.description"
                  name="description"
                  :label="$t('description')"
                  rows="4"
                />
              </template>
            </FormGroup>
          </div>
          <div class="d-flex justify-end w-100 gap-md">
            <v-btn
              outlined
              color="primary"
              min-width="120"
              @click="dialog = false"
              >{{ $t("cancel") }}</v-btn
            >
            <v-btn
              class="primary"
              type="submit"
              min-width="120"
              :loading="loading"
              >{{ $t("save") }}</v-btn
            >
          </div>
        </v-form>
      </ValidationObserver>
    </CustomDialog>
  </section>
</template>


<script>
import form from "@/mixins/form";
export default {
  name: "CarouselForm",
  mixins: [form],
  data() {
    return {
      form: {
        nameAr: "",
        nameEn: "",
        image: "",
        description: "",
        orderPriority: "",
      },
      imagePreview: null,
    };
  },
  methods: {
    handleUpdatePhoto(item) {
      this.form.image = item.file;
      this.imagePreview = item.imageUrl;
    },
  },
};
</script>