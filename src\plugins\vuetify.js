import Vue from "vue";
import Vuetify from "vuetify/lib/framework";
import ar from "vuetify/lib/locale/ar";
import ArrowDown from '@/assets/icons/arrow-down'
import ArabicIcon from '@/assets/icons/arabicIcon'
import EnglishIcon from '@/assets/icons/englishIcon'
import MenuIcon from "@/assets/icons/menuIcon"
import UploadIcon from "@/assets/icons/uploadIcon"
import theme from "@/utils/theme"
Vue.use(Vuetify);


export default new Vuetify({
  rtl: true,
  icons: {
    iconfont: 'mdi',
    values: {
      arrowDown: {
        component: ArrowDown
      },
      menuIcon: {
        component: MenuIcon
      },

      arabicIcon: {
        component: ArabicIcon
      },
      englishIcon: {
        component: EnglishIcon
      },
      uploadIcon: {
        component: UploadIcon
      }
    }
  },

  theme: {
    themes: theme,
  },
  lang: {
    current: "ar",
    locales: { ar }
  }
});
