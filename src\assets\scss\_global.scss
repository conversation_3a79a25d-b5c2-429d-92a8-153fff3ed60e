@font-face {
  font-family: "Cairo";
  src: url('../fonts/Cairo.ttf');
}

* {
  font-family: 'Cairo' !important;
}
.theme--light{
  .v-main{
    background-color: #f9f9f6 !important;

  }
  .v-menu__content {
    box-shadow: rgba(89, 102, 122, 0.2) 0px 0px 20px !important;
    .v-list.v-select-list {
      background-color: #f0fafd !important;
    }
  }
}
.v-application--wrap {
  display: flex;
  flex-direction: row;
}



[dir="ltr"] .mobile-layout {
  padding-left: 15rem;
}

[dir="rtl"] .mobile-layout {
  padding-right: 15rem;
}


.v-input__slot {
  border-radius: 8px !important;
  // box-shadow: 0px 4px 20px 0px #0000001a;


}

.normal-letter-spacing {
  letter-spacing: initial;
}



.page {
  padding: 1rem;
}
.bounce {
  animation: movebounce 5s linear infinite;
}
.move-card {
  animation: moveCard 0.8s linear;
}
.radius-8{
  border-radius: 8px;
}

.Vue-Toastification__toast--default{
  background-color: #fff !important;
  color: var(--primary) !important;
  .Vue-Toastification__progress-bar{
    background-color: var(--primary) !important;
    height: 3px;
  }
  .Vue-Toastification__close-button{
    color: var(--primary) !important;
  }
}



.v-application--is-ltr .v-list-group--no-action > .v-list-group__items > .v-list-item{
  padding-left: 16px !important;
}

.v-application--is-rtl .v-list-group--no-action > .v-list-group__items > .v-list-item{
  padding-right: 16px !important;
}



.v-data-table > .v-data-table__wrapper > table > thead > tr:last-child > th{
  border-bottom: unset !important;
}


::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background-color: #fdf9f9;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(13,164,135,0.5);
  border-radius: 5px;
}
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(13,164,135,0.1);
  box-shadow: inset 0 0 6px rgba(13,164,135,0.1);
  background-color: #f5f5f5;
  border-radius: 5px;
}



.login-page {
  .success-msg,
  .v-messages__message {
      @include sm-and-down {
          font-size: 9px !important;
      }
      @include langLtr {
          text-align: right;
      }
  }
  @include langLtr {
      direction: rtl;
  }
}

.w-100{
  width: 100%;
}

.pointer{
  cursor: pointer;
}


.page-card{
  min-height: calc(100vh - 160px);
}


.v-chip--select{
  background-color: var(--primary) !important;
  color: #fff !important;
}


.size-12{
  font-size: 12px;
}
.size-14{
  font-size: 14px;
}


.size-16{
  font-size: 16px;
}


.size-18{
  font-size: 18px;
}



.theme--light .card-detail{
p{
  color: #777777;
    font-weight: 400;
    font-size: 14px;
    text-transform: capitalize;
    margin-bottom: 0;
    line-height: 18px;
}
h5{
  margin-top: 10px;
  color: #212529;
  font-size: calc(15px + (16 - 15) * ((100vw - 320px) / (1920 - 320)));
    line-height: 1.2;
    margin: 10px 0 0 0;
    font-weight: 400;
}
}

.theme--light .seperated-label {
  background-color: var(--primary);
  border-radius: 0.25rem;
  color: white;
  text-align: center;
  padding: 0.5rem;
  min-width: 8rem;
  height: 2.5rem;
}

.gap-md{
  gap: 16px;
}
.gap-sm{
  gap: 8px;
}


a{
  text-decoration: none !important;
}



.expand-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(22rem, 1fr));
  span {
    text-align: initial;
  }
}