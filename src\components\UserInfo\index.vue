<template>
  <div>
    <v-card>
      <v-card-text>
        <div>
          <span class="text-capitalize mr-1 font-weight-bold size-20">
            {{ $t("name") }} :
          </span>
          <span class="size-14 font-weight-bold">{{ userData?.name }}</span>
        </div>
        <div class="mt-2">
          <div>
            <span class="text-capitalize mr-1 font-weight-bold size-20">
              {{ $t("email") }} :
            </span>
            <span>{{ userData?.email }}</span>
          </div>
        </div>
        <div class="mt-2">
          <span class="text-capitalize mr-1 font-weight-bold size-20">
            {{ $t("phone") }} :
          </span>
          <span>{{ userData?.phone }}</span>
        </div>
        <CustomTable
          v-if="rendering"
          :tableHeaders="headers"
          :itemsData="reservations"
          :fromStore="false"
          :showPagination="false"
          :slotColumnNames="[
            '#',
            'creationDate',
            'from',
            'fromDate',
            'to',
            'toDate',
            'pnr',
          ]"
          @handleClick="handleClick"
          :isClickable="true"
        >
          <template #item.#="{ item, index }">
            {{ index + 1 }}
          </template>
          <template #item.creationDate="{ item }">
            <span>{{
              convertDate(item.flights[0].creationDate, "LL HH:mm")
            }}</span>
          </template>
          <template #item.from="{ item }">
            <span>{{
              item.flights[0].flightDetails[0].departure_iataCode
            }}</span>
          </template>
          <template #item.fromDate="{ item }">
            <span>{{
              convertDate(
                item.flights[0].flightDetails[0].departure_at,
                "LL HH:mm"
              )
            }}</span>
          </template>

          <template #item.to="{ item }">
            <span>{{
              item.flights[0].flightDetails[
                item.flights[0].flightDetails.length - 1
              ].arrival_iataCode
            }}</span>
          </template>

          <template #item.toDate="{ item }">
            <span>{{
              convertDate(
                item.flights[0].flightDetails[
                  item.flights[0].flightDetails.length - 1
                ].arrival_at,
                "LL HH:mm"
              )
            }}</span>
          </template>
          <template #item.pnr="{ item }">
            <span>{{ item.flights[0].reference }}</span>
          </template>
        </CustomTable>
      </v-card-text>
    </v-card>
    <ReservationDialog
      v-model="dialog"
      :reservationGUID="reservationId"
      v-if="dialog"
      @close-dialog="dialog = false"
    />
  </div>
</template>

<script>
import ReservationDialog from "@/components/Reservations/Dialog/index.vue";
export default {
  name: "MainData",
  components: { ReservationDialog },
  props: {
    userData: {
      type: Object,
      default: () => {},
    },
    reservations: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      rendering: true,
      dialog: false,
      reservationId: null,
    };
  },
  computed: {
    headers() {
      const headers = [
        {
          name: "#",
          value: "#",
          showColumn: true,
          width: 25,
        },
        {
          name: "creationDate",
          value: "creationDate",
          sortable: false,
          showColumn: true,
        },
        {
          name: "from",
          value: "from",
          sortable: false,
          showColumn: true,
        },
        {
          name: "fromDate",
          value: "fromDate",
          sortable: false,
          showColumn: true,
        },
        {
          name: "to",
          value: "to",
          sortable: false,
          showColumn: true,
        },
        {
          name: "toDate",
          value: "toDate",
          sortable: false,
          showColumn: true,
        },
        {
          name: "pnr",
          value: "pnr",
          sortable: false,
          showColumn: true,
        },
      ];
      return headers.filter((head) => head.showColumn);
    },
  },
  methods: {
    handleClick(item) {
      this.reservationId = item.reservationGUID;
      this.dialog = true;
    },
  },
};
</script>
