<template>
  <main class="mt-4">
    <v-container>
      <v-card class="page-card">
        <v-card-text>
          <v-row>
            <v-col cols="12">
              <CustomTable
                v-if="rendering"
                :tableHeaders="headers"
                reqName="reservation/paginated"
                :slotColumnNames="[
                  '#',
                  'creationDate',
                  'from',
                  'fromDate',
                  'to',
                  'toDate',
                  'pnr',
            'isActive',
            'isPaid',
            'isRefunded',

                ]"
                :title="$t('reservations')"
                @handleClick="handleClick"
                :isClickable="true"
              >
                <template #top-left>
                  <InputText
                    v-model="search"
                    name="search"
                    hide-details
                    outlined
                    class="mx-4"
                    dense
                    clearable
                    :label="$t('search')"
                    @input="setSearchQuery"
                  >
                  </InputText>
                </template>
                <template #item.#="{ item, index }">
                  {{ index + pagination.from }}
                </template>
                <template #item.creationDate="{ item }">
                  <span>{{
                    convertDate(item.flights[0].creationDate, "LL HH:mm")
                  }}</span>
                </template>
                <template #item.from="{ item }">
                  <span>{{
                    item.flights[0].flightDetails[0].departure_iataCode
                  }}</span>
                </template>
                <template #item.fromDate="{ item }">
                  <span>{{
                    convertDate(
                      item.flights[0].flightDetails[0].departure_at,
                      "LL HH:mm"
                    )
                  }}</span>
                </template>

                <template #item.to="{ item }">
                  <span>{{
                    item.flights[0].flightDetails[
                      item.flights[0].flightDetails.length - 1
                    ].arrival_iataCode
                  }}</span>
                </template>

                <template #item.toDate="{ item }">
                  <span>{{
                    convertDate(
                      item.flights[0].flightDetails[
                        item.flights[0].flightDetails.length - 1
                      ].arrival_at,
                      "LL HH:mm"
                    )
                  }}</span>
                </template>
                <template #item.pnr="{ item }">
                  <span>{{item.flights[0].reference }}</span>
                </template>
                <!--
                <template #item.ticketEn="{ item }">
                  <v-btn
                    :href="item.ticketEn"
                    target="_blank"
                    text
                    color="primary"
                  >
                    <v-icon>mdi-ticket</v-icon>
                  </v-btn>
                </template>
                <template #item.#="{ item, index }">
                  {{ index + pagination.from }}
                </template> -->
                <template #item.isActive="{ item }">
            <span>{{item.isActive ? "نعم" : "لا"}}</span>
          </template>
          <template #item.isPaid="{ item }">
            <span>{{item.isPaid ? "نعم" : "لا"}}</span>
          </template>
          <template #item.isRefunded="{ item }">
            <span>{{item.isRefunded ? "نعم" : "لا"}}</span>
          </template>

              </CustomTable>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-container>
  </main>
</template>

<script>
import { mapActions } from "vuex";
export default {
  name: "ReservationsRecord",
  data() {
    return {
      rendering: true,
      search: "",
    };
  },
  computed: {
    headers() {
      const headers = [
        {
          name: "#",
          value: "#",
          showColumn: true,
          width: 25,
        },
        {
          name: "creationDate",
          value: "creationDate",
          sortable: false,
          showColumn: true,
        },
        {
          name: "from",
          value: "from",
          sortable: false,
          showColumn: true,
        },
        {
          name: "fromDate",
          value: "fromDate",
          sortable: false,
          showColumn: true,
        },
        {
          name: "to",
          value: "to",
          sortable: false,
          showColumn: true,
        },
        {
          name: "toDate",
          value: "toDate",
          sortable: false,
          showColumn: true,
        },
        {
          name: "pnr",
          value: "pnr",
          sortable: false,
          showColumn: true,
        },
        {
          name: "نشطة",
          value: "isActive",
          sortable: false,
          showColumn: true,
        },
        {
          name: "مدفوعة",
          value: "isPaid",
          sortable: false,
          showColumn: true,
        },
        {
          name: "تم استردادها",
          value: "isRefunded",
          sortable: false,
          showColumn: true,
        }

      ];
      return headers.filter((head) => head.showColumn);
    },
  },
  methods: {
    ...mapActions(["setQuery"]),
    setSearchQuery() {
      console.log(this.search);
      let query = {
        SearchString: this.search,
      };
      this.setQuery(query);
      // this.rendering = false;
      // setTimeout(() => {
      //   this.rendering = true;
      // }, 10);
    },
    handleClick(item) {
      this.$router.push(`/reservations/${item.reservationGUID}`);
    },
  },
};
</script>
