import axios from "@/config/axios";
export const form = {
  props: ['id', 'url'],
  data() {
    return {
      dialog: false,
      loading: false,
      loadingData: false,
      defaultForm: null,
    };
  },
  computed: {
    textBtn() {
      return this.id ? this.$t('update') : this.$t('create')
    }
  },
  methods: {
    buildData() {
      const formData = new FormData();
      for (const key in this.form) {
        formData.append(key, this.form[key]);
      }
      return formData;
    },
    resetForm() {
      this.dialog = false
      this.$refs.form.reset();
      this.$eventBus.$emit("on-refetch-data");
      this.form = { ...this.defaultForm }
    },
    clonedForm() {
      this.defaultForm = { ...this.form }
    },
    async handleShow() {
      this.dialog = true
      this.loadingData = true
      const response = await axios.get(`${this.url}/get-by-id?Id=${this.id}`);
      this.form = response.data.data
      this.loadingData = false
    },
    handleCreate() {
      axios.post(`${this.url}/create`, this.buildData()).then(() => {
        this.resetForm();
      }).catch((err) => console.log(err)).finally(() => this.loading = false)
    },
    handleUpdate() {
      axios.put(`${this.url}/update`, this.buildData()).then(() => {
        this.resetForm()
      }).catch((err) => console.log(err)).finally(() => this.loading = false)
    },
    submitForm() {
      this.$refs.form.reset();
      this.$refs.form.validate().then((success) => {
        if (success && !this.serverErrors) {
          this.loading = true;
          this.id ? this.handleUpdate() : this.handleCreate()
        }
      })
    },
  },
  mounted() {
    this.clonedForm()
  }
};


export default form