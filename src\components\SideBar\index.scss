
@import "@/assets/scss/mixins";

.layout{
  &__drawer{
    background-size: 400% 400%;
    overflow: hidden;
    max-height: unset !important;
    background: linear-gradient(-45deg,#1565C0,#1976D2
    )!important;
    &--logo{
      &-wrapper{
          border: 3px solid #E3F2FD;
          width: 79px;
          height: 79px;
          // padding: 7px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          img{
            border-radius: 50%;
            object-fit: contain;

          }
        
      }
      &-menu{
        width: 24px;
        height: 24px;
        
        svg{
          width: 100% !important;
          height: 100% !important;
          path{
            fill: #fff !important;
          }
        }
      }
      
    }

    max-height: inherit !important;
    .v-list-group__header.v-list-item--active {
        background: transparent !important;
        border-left: unset !important;
    }
    .v-list-item{
      .v-btn .v-icon{
        color: #fff !important;
    }
    .v-icon{
      color: #fff;
     }
    }

    .v-list-item--active {
        background-color: rgba(#BB<PERSON><PERSON> , 0.2) !important;
        border-left: 3px solid #fff;
        color: var(--primary) !important;
    
    }
    .v-list-item--link:before {
        background: initial !important;
    }
    .new-notification{
      background-color: #ff0000;
      width: 24px;
      height: 24px;
      border-radius: 50%;
    }

  }
  &__header{
    padding: 22px 16px;
    .v-toolbar__content{
      height: 46px !important;
      padding: 0 !important;
    }
    .v-input__slot{
      fieldset {
        border: none !important;
      }
    }
    &--search{
      background-color: #ffa53b;
      width: 60px;
      height: 100%;
      margin: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8px !important;
      border-bottom-right-radius: unset !important;
      border-top-right-radius: unset !important;
      
      @include langLtr{
        border-radius: 8px !important;
        border-bottom-left-radius: unset !important;
        border-top-left-radius: unset !important;

      }
    }
  }
  &__footer{
    box-shadow: 0 0 20px rgba(89,102,122,0.1) !important;
    text-align: center;
    padding: 15px;
    right: unset !important;
    @include langLtr{
      left: unset !important;
    }
    p{
      width: 100%;
      margin: 0 !important;
    }
  }
}

.theme--light{

  .layout__header {
   background-color: #fff !important;
  }
  .layout__footer{
   background: #fff !important;
  }
 }



.theme--dark{
  .layout__drawer {
    .v-list-item{
     .v-btn .v-icon{
       color: rgba(255,255,255,.87) !important;
   }

    }
    // .v-list-item--active {
    //   .v-btn .v-icon{
    //     color: var(--primary) !important;
    //   }
    // }
  }

}