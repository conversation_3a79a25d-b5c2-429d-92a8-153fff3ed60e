<template>
  <main class="mt-4">
    <v-container>
      <v-card class="page-card">
        <v-card-text>
          <v-row>
            <v-col cols="12">
              <CustomTable
                v-if="rendering"
                :tableHeaders="headers"
                :reqName="`${url}/paginated?GetActiveOnly=false`"
                :slotColumnNames="['#', 'isActive', 'actions']"
                :title="$t('countries')"
              >
                <template #top-right>
                  <CountryForm :url="url" />
                </template>
                <template #top-left>
                  <InputText
                    v-model="search"
                    name="search"
                    hide-details
                    outlined
                    class="mx-4"
                    dense
                    clearable
                    :label="$t('search')"
                    @input="setSearchQuery"
                  >
                  </InputText>
                </template>
                <template #item.#="{ item, index }">
                  {{ index + pagination.from }}
                </template>
                <template #item.isActive="{ item }">
                  <toggle-service
                    :is-edit="true"
                    :model-name="`${url}/activate-deactivate`"
                    :model-id="item.id"
                    v-model="item.isActive"
                  />
                </template>
                <template #item.actions="{ item }">
                  <CountryForm :url="url" :id="item.id" />
                </template>
              </CustomTable>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-container>
  </main>
</template>



<script>
import CountryForm from "@/components/Forms/Country";
import { mapActions } from "vuex";

export default {
  name: "CountriesRecord",
  components: {
    CountryForm,
  },
  data() {
    return {
      rendering: true,
      url: "country",
      search: "",
    };
  },
  computed: {
    headers() {
      const headers = [
        {
          name: "#",
          value: "#",
          showColumn: true,
        },
        {
          name: "name",
          value: "name",
          sortable: false,
          showColumn: true,
        },
        {
          name: "code",
          value: "code",
          sortable: false,
          showColumn: true,
        },

        {
          name: "active",
          value: "isActive",
          sortable: false,
          showColumn: true,
        },

        {
          name: "actions",
          value: "actions",
          showColumn: true,
        },
      ];
      return headers.filter((head) => head.showColumn);
    },
  },
  methods: {
    ...mapActions(["setQuery"]),
    setSearchQuery() {
      console.log(this.search);
      let query = {
        SearchString: this.search,
      };
      this.setQuery(query);
      // this.rendering = false;
      // setTimeout(() => {
      //   this.rendering = true;
      // }, 10);
    },
  },
};
</script>