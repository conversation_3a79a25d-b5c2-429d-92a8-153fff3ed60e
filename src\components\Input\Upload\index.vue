<template>
  <main>
    <label v-if="label">
      <strong class="font-size-14">{{ label }}</strong>
    </label>
    <input
      ref="file"
      type="file"
      hidden
      :id="name"
      accept="image/*"
      @change="onFileChange"
    />
    <div class="d-flex mt-1">
      <div @click="$refs.file.click()" class="w-100">
        <v-btn
          class="text-lowercase"
          style="background-color: #fff; border: 1px solid #a8a8a8"
          outlined
          width="100%"
          height="40"
        >
          <div class="d-flex align-center flex-column flex-sm-row">
            <v-icon left v-text="'$uploadIcon'"></v-icon>
            <span class="font-size-12" style="color: #a8a8a8">
              <span style="color: #454f5b">{{ $t("browse_files") }}</span>
            </span>
          </div>
        </v-btn>
        <span>{{ imageName }}</span>

        <div class="font-size-12 white--text" v-if="hint" v-text="hint"></div>
        <!-- <small style="font-size: 58%">
            {{ $t("validation.max_size_image") }}
          </small> -->
      </div>
    </div>
  </main>
</template>

<script>
export default {
  props: {
    file_name: {
      type: String,
      default: "",
    },
    reqType: {
      type: String,
      default: "",
    },
    maxSize: {
      type: Number,
      default: 2,
    },
    imgUrl: {
      type: String,
      default: null,
    },
    imgId: {
      default: null,
    },
    width: {
      type: Number,
      default: null,
    },
    height: {
      type: Number,
      default: null,
    },
    maxHeight: {
      type: Number,
      default: 200,
    },
    minHeight: {
      type: Number,
      default: 200,
    },
    reset: {
      type: Boolean,
      default: false,
    },
    resetToggle: {
      type: Boolean,
      default: false,
    },
    fieldName: {
      type: String,
      default: "",
    },
    value: {
      type: String,
    },
    label: {
      type: String,
      default: "",
    },
    name: {
      type: String,
      default: "",
    },
    hint: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      imageName: null,
      ImageUrl: null,
      errorDialog: null,
      errorText: "",
      errorType: "",

      absolute: false,
      overlay: false,
      zIndex: 990,
    };
  },
  computed: {
    ImageErrorMsg() {
      if (this.errorType === "MIME") {
        return this.$t("validation.imageType");
      } else if (this.errorType === "SIZE") {
        return this.$t("validation.imageSize", { count: this.maxSize });
      } else if (this.errorType === "DIMENSIONS") {
        return this.$t("validation.imageDimensions", {
          width: this.width,
          height: this.height,
        });
      } else {
        return "";
      }
    },
  },
  watch: {
    reset: {
      handler(value) {
        if (value) {
          this.resetImageFile();
        }
      },
      immediate: true,
    },

    imgUrl: {
      handler(newValue) {
        if (newValue && typeof newValue === "string") {
          this.ImageUrl = newValue;
        }
      },
      immediate: true,
    },
  },
  methods: {
    onFileChange(e) {
      if (e.target.files.length > 0) {
        const imageFile = e.target.files[0];
        // with Dimensions
        this.readImage(imageFile);
      }
    },
    readImage(imageFile) {
      this.imageName = imageFile.name;
      this.ImageUrl = URL.createObjectURL(imageFile);
      if (imageFile) {
        const dataObj = {
          file: imageFile,
        };
        if (this.fieldName) {
          dataObj.name = this.fieldName;
          this.$emit("fileSelected", dataObj);
        } else {
          this.$emit("fileSelected", dataObj);
        }
      }
    },
    resetImageFile() {
      this.ImageUrl = null;
      this.$refs.file.value = "";
    },
    resetData() {
      this.$refs.file.value = "";
    },
  },
};
</script>

<style lang="scss"></style>
