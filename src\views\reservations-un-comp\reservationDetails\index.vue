<template>
  <div>
    <v-container>
      <h1>{{ $t("reservationDetails") }}</h1>
  
      <TabsHorizontal :tabs="items">
        <template>
          <v-tab-item value="tab-0">
            <MainData :reservation="reservation" :travellers="travelers" />
            <ResevationsInfo
              :flightDetails="reservation?.flights[0]?.flightDetails"
            />
          </v-tab-item>
          <v-tab-item value="tab-1">
            <PaymentInfo :payments="reservation?.payments" />
          </v-tab-item>
        </template>
      </TabsHorizontal>
    </v-container>
  </div>
</template>

<script>
import TabsHorizontal from "@/components/Tabs/Horizontal";
import MainData from "@/components/Reservations/MainData";
import PaymentInfo from "@/components/Reservations/payment";
import ResevationsInfo from "@/components/Reservations/Info";
export default {
  name: "ReservationDetails",
  components: {
    MainData,
    ResevationsInfo,
    PaymentInfo,
    TabsHorizontal,
  },
  data() {
    return {
      tab: null,
      reservation: null,
      loading: false,
      travelers: [],

      items: [
        {
          title: this.$t("main_data"),
          name: "MainData",
     
        },
        {
          title: this.$t("payment_info"),
          name: "PaymentInfo",
        },
        // {
        //   title: this.$t("travellers"),
        //   name: "Travellers",
        //   props: { travellers: this.travelers },
        // },
      ],
    };
  },
  computed: {
    reservationGUID() {
      return this.$route.params.reservationGUID;
    },
  },

  created() {
    this.getResevations();
  },

  methods: {
    getResevations() {
      this.loading = true;
      this.$http
        .get({
          reqName: `/reservation/get-by-id?ReservationGUID=${this.reservationGUID}`,
        })
        .then(({ data: response }) => {
          console.log(response.data);
          this.reservation = response.data;
          this.travelers = response.data.travelers;
          this.loading = false;
        });
    },
  },
};
</script>
