export const settingsRoutes = {
  path: "/",
  to: "/",
  icon: "mdi-cogs",
  activeRoutes: ["record_system", "record_currencies"],
  meta: {
    title: "settings",
    // permissions: "",
  },
  order: 4,
  allow_children: true,
  showInMenu: true,
  name: "settings",
  component: () => import("@/views/settings"),
  children: [
    {
      path: "/system-settings",
      to: "/system-settings",
      component: () => import("@/views/settings/system/index"),
      name: "system-settings",
      icon: "mdi-flag",
      allowed: true,
      meta: {
        title: "system_settings",
        // permissions: 7
      },
      activeRoutes: ["record_system"], // Specify the activeRoutes for the "cities" route

      children: [
        {
          path: "",
          component: () => import("@/views/settings/system/record/index"),
          name: "record_system",
          icon: "arrow.svg",
          to: "/countries",
          allowed: true,
          meta: {
            title: "record_system",
            // permissions: 7
          },
        }

      ],
    },
    {
      path: "/currencies",
      to: "/currencies",
      component: () => import("@/views/settings/currency/index"),
      name: "currencies",
      icon: "mdi-cash",
      allowed: true,
      meta: {
        title: "currencies",
        // permissions: 7
      },
      activeRoutes: ["record_currencies"], // Specify the activeRoutes for the "cities" route

      children: [
        {
          path: "",
          component: () => import("@/views/settings/currency/record/index"),
          name: "record_currencies",
          icon: "arrow.svg",
          to: "/currencies",
          allowed: true,
          meta: {
            title: "record_currencies",
            // permissions: 7
          },
        }

      ],
    },



  ]
};
