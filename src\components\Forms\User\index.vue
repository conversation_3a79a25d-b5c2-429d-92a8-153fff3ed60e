<template>
  <section>
    <v-btn min-width="20px" text v-if="id" @click="handleShow">
      <v-icon color="primary"> mdi-pencil</v-icon>
    </v-btn>
    <v-btn @click="dialog = true" color="primary" depressed class="mb-4" v-else>
      {{ $t("new_user") }}
    </v-btn>
    <CustomDialog :dialog="dialog" @close-dialog="dialog = false">
      <template #header>
        <DialogHeader :title="$t('new_user')" />
      </template>
      <ValidationObserver ref="form" v-slot="{ handleSubmit }">
        <v-form @submit.prevent="handleSubmit(submitForm)">
          <div class="dialog-form">
            <FormGroup rules="required" name="name" errorName="Name">
              <template #default="{ attrs }">
                <InputText
                  v-bind="attrs"
                  v-model="form.name"
                  focus
                  name="name"
                  :label="$t('name')"
                />
              </template>
            </FormGroup>
            <FormGroup
              rules="required"
              name="emailOrPhone"
              errorName="EmailOrPhone"
            >
              <template #default="{ attrs }">
                <InputText
                  v-bind="attrs"
                  v-model="form.emailOrPhone"
                  name="emailOrPhone"
                  :label="$t('emailOrPhone')"
                />
              </template>
            </FormGroup>
            <FormGroup rules="required" name="role" errorName="RoleId">
              <template #default="{ attrs }">
                <InputSelect
                  :items="[
                    { id: '1', name: 'Admin' },
                    { id: '3', name: 'Manager' },
                  ]"
                  v-bind="attrs"
                  v-model="form.roleId"
                  name="role"
                  :label="$t('role')"
                  item-text="name"
                  item-value="id"
                />
              </template>
            </FormGroup>
            <FormGroup
              rules="required"
              name="password"
              errorName="Password"
              v-if="!id"
            >
              <template #default="{ attrs }">
                <InputPassword
                  v-bind="attrs"
                  v-model="form.password"
                  name="password"
                  :label="$t('password')"
                />
              </template>
            </FormGroup>
          </div>
          <div class="d-flex justify-end w-100 gap-md">
            <v-btn
              outlined
              color="primary"
              min-width="120"
              @click="dialog = false"
              >{{ $t("cancel") }}</v-btn
            >
            <v-btn
              class="primary"
              :loading="loading"
              type="submit"
              min-width="120"
              >{{ $t("save") }}</v-btn
            >
          </div>
        </v-form>
      </ValidationObserver>
    </CustomDialog>
  </section>
</template>


<script>
import form from "@/mixins/form";
export default {
  name: "CountryForm",
  mixins: [form],
  data() {
    return {
      form: {
        name: "",
        emailOrPhone: "",
        password: "",
        roleId: "",
      },
    };
  },
  methods: {
    async handleShow() {
      this.dialog = true;
      this.loadingData = true;
      const response = await this.$http.get({
        reqName: `${this.url}/get-by-id?Id=${this.id}`,
      });

      this.form = response.data.data;
      this.form.emailOrPhone =
        response.data.data.phone || response.data.data.email;

      this.loadingData = false;
    },
  },
};
</script>