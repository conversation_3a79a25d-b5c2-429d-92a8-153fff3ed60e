<template>
  <main>
    <v-navigation-drawer
      v-model="drawer"
      :width="drawerWidth"
      :right="right"
      :mini-variant="miniVariant"
      :clipped="clipped"
      mini-variant-width="80"
      fixed
      app
      class="layout__drawer pa-4"
    >
      <v-list
        class="layout__drawer--logo d-flex align-center justify-space-between"
      >
        <!-- img -->
        <v-list-item target="_blank" v-if="!miniVariant">
          <div class="layout__drawer--logo-wrapper mx-auto">
            <img
              :src="require('@/assets/imgs/tayar-logo.png')"
              height="100%"
              class="mx-auto"
              width="100%"
              alt="tayar logo"
            />
          </div>
        </v-list-item>
        <!-- toggle -->
        <v-list-item
          @click="handleSwitch"
          class="justify-end layout__drawer--logo-menu"
        >
          <v-icon size="24" v-text="'$menuIcon'"></v-icon>
        </v-list-item>
      </v-list>
      <v-list>
        <template v-for="(item, index) in items">
          <v-list-group
            v-if="item.allow_children && item.children.length > 0"
            :key="index"
            :no-action="!miniVariant"
            :value="item.activeRoutes.includes($route.meta.title)"
            :class="{
              'active-group': item.allow_children,
            }"
          >
            <template #activator>
              <ToolTip
                color="primary"
                :icon="item.icon"
                :text="$t(`${item.meta.title}`)"
                :disabled="!miniVariant"
              >
              </ToolTip>
              <v-list-item-content>
                <v-list-item-title class="white--text">
                  {{ $t(`${item.meta.title}`) }}</v-list-item-title
                >
              </v-list-item-content>
            </template>
            <template v-for="(child, index) in item.children">
              <v-list-item
                v-if="child.allowed && !item.hidden && !child.hidden"
                :key="index"
                :to="child.to"
                @click="handleClick(child)"
              >
                <ToolTip
                  color="primary"
                  :icon="child.icon"
                  :text="$t(`${child.meta.title}`)"
                  :disabled="!miniVariant"
                >
                </ToolTip>
                <v-list-item-content>
                  <v-list-item-title
                    class="white--text"
                    v-text="$t(`${child.meta.title}`)"
                  ></v-list-item-title>
                </v-list-item-content>
              </v-list-item>
            </template>
          </v-list-group>
          <v-list-item
            v-else-if="item.showInMenu && !item.allow_children"
            @click="handleClick(item)"
            :key="item.meta.title"
            :to="item.to"
            :ripple="!item.icon ? false : true"
            :style="{ cursor: !item.icon ? 'initial' : 'pointer' }"
          >
            <ToolTip
              :noIcon="item.icon ? true : false"
              color="primary"
              :icon="item.icon"
              :text="$t(`${item.meta.title}`)"
              :disabled="!miniVariant"
            >
            </ToolTip>
            <v-list-item-content>
              <v-list-item-title
                class="white--text"
                style="padding: 0 10px"
                v-text="$t(`${item.meta.title}`)"
              >
              </v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </template>
      </v-list>
    </v-navigation-drawer>
    <v-app-bar
      fixed
      app
      flat
      min-height="80"
      style="overflow: hidden"
      class="layout__header"
    >
      <v-app-bar-nav-icon
        class="hidden-lg-and-up"
        @click="drawer = !drawer"
      ></v-app-bar-nav-icon>

      <v-spacer />
      <div class="d-flex justify-center align-center">
        <Notifications />
        <v-btn icon class="mx-1" @click="handleChangeLanguage">
          <v-icon
            v-text="$i18n.locale == 'en' ? '$arabicIcon' : '$englishIcon'"
          />
        </v-btn>
        <v-btn icon class="mx-1" @click="toggleDarkTheme">
          <v-icon size="24">mdi-weather-night</v-icon>
        </v-btn>
        <v-menu bottom min-width="200px" rounded offset-y open-on-hover>
          <template v-slot:activator="{ on }">
            <v-btn icon x-large v-on="on">
              <v-avatar>
                <img :src="user.imageURL" alt="user" v-if="user?.imageURL" />
                <img
                  src="https://cdn.vuetifyjs.com/images/john.jpg"
                  alt="John"
                  v-else
                />
              </v-avatar>
            </v-btn>
          </template>
          <v-card>
            <v-list-item-content class="justify-center">
              <div class="mx-auto text-center">
                <v-avatar>
                  <img :src="user.imageURL" alt="user" v-if="user?.imageURL" />
                  <img
                    src="https://cdn.vuetifyjs.com/images/john.jpg"
                    alt="John"
                    v-else
                  />
                </v-avatar>
                <h4>{{ user?.roleName }}</h4>
                <p class="text-caption mt-1 mb-1 size-14">{{ user?.name }}</p>
                <p class="mb-3 size-14">{{ user?.email }}</p>

                <v-divider class="my-1"></v-divider>
                <v-btn depressed rounded text @click="logout">
                  {{ $t("logout") }}
                </v-btn>
              </div>
            </v-list-item-content>
          </v-card>
        </v-menu>
      </div>
    </v-app-bar>
    <v-footer
      fixed
      bottom
      app
      :style="{
        width: `calc(100% - ${footerWidth}) !important`,
        footerPosition,
      }"
      class="layout__footer"
    >
      <p class="primary--text">
        {{ $t("copyright") }} {{ new Date().getFullYear() }} &copy;
        {{ $t("tayar") }}
      </p>
    </v-footer>
  </main>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
import Cookies from "js-cookie";
import { getLanguage, setLanguage } from "@/utils/languages";
import Notifications from "@/components/Notifications";
export default {
  name: "SideBar",
  components: {
    Notifications,
  },
  data() {
    return {
      clipped: false,
      drawer: true,
      miniVariant: false,
      drawerWidth: 280,
      right: true,
      form: {
        search: "",
        branch: "",
      },
    };
  },
  computed: {
    ...mapGetters({
      items: "getRoutes",
      user: "getUser",
    }),
    profileDropdown() {
      return [{ title: this.$t("profile"), to: "/profile" }];
    },
    footerWidth() {
      if (this.drawer) {
        return !this.miniVariant ? "280px" : "80px";
      } else {
        return "0px";
      }
    },
    footerPosition() {
      if (this.$i18n.locale === "ar") {
        return { left: `${this.miniVariant ? "80px" : "280px"} !important` };
      } else {
        return { right: `${this.miniVariant ? "80px" : "280px"} !important` };
      }
    },
  },
  created() {
    this.handleDirection();
  },
  methods: {
    handleDirection() {
      if (getLanguage() == "ar") {
        this.right = true;
        this.$vuetify.rtl = true;
      } else {
        this.right = false;
        this.$vuetify.rtl = false;
      }
    },
    handleChangeLanguage() {
      this.handleDirection();
      if (this.$i18n.locale == "ar") {
        setLanguage("en");
      } else {
        setLanguage("ar");
      }
      window.location.reload();
    },
    toggleDarkTheme() {
      this.$vuetify.theme.dark = !this.$vuetify.theme.dark;
      Cookies.set("theme_mode", this.$vuetify.theme.dark ? "dark" : "light");
    },
    handleSwitch() {
      if (!this.$vuetify.breakpoint.mobile) {
        this.miniVariant = !this.miniVariant;
      } else {
        this.drawer = !this.drawer;
      }
    },
    handleClick({ to }) {
      if (this.getQuery && Object.keys(this.getQuery).length > 0) {
        this.$store.dispatch("removeQuery", null);

        this.$eventBus.$emit("on-refetch-data");
      }
      this.$eventBus.$off("on-refetch-data");
      this.$eventBus.$emit("on-sidebar-route-clicked", to);
    },
    logout() {
      this.$store
        .dispatch("Logout")
        .then(() => {
          this.$router.push("/login");
        })
        .catch((error) => {
          console.log(error);
        });
    },
    // removeToken() {
    //   const formData = new FormData();
    //   formData.append("DeviceToken", Cookies.get("device_token"));
    //   this.$http
    //     .post({
    //       reqName: "auth/sign-out",
    //       data: formData,
    //     })
    //     .then(() => {
    //       Cookies.remove("device_token");
    //     });
    // },
  },
  watch: {
    "$i18n.locale": {
      handler(value) {
        if (value === "en") {
          this.$vuetify.rtl = false;
          this.right = false;
        } else {
          this.$vuetify.rtl = true;
          this.right = true;
        }
      },
      immediate: true,
      deep: true,
    },
    "$vuetify.breakpoint.mobile": {
      handler(val) {
        if (val) {
          this.drawer = false;
          this.miniVariant = false;
        }
      },
      immediate: true,
      deep: true,
    },
  },
};
</script>

<style lang="scss">
@import "index.scss";
</style>
