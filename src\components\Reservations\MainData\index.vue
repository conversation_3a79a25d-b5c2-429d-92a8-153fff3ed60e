<template>
  <div>
    <v-card>
      <v-card-text>
        <div>
          <span class="text-capitalize mr-1 font-weight-bold size-20">
            bookingId :
          </span>
          <span class="size-14 font-weight-bold">{{ reservation?.bookingId }}</span>
        </div>
        <div class="mt-2 d-flex align-center justify-space-between">
          <div>
            <span class="text-capitalize mr-1 font-weight-bold size-20">
              reservationGUID :
            </span>
            <span>{{ reservation?.reservationGUID }}</span>
          </div>
          <div>
            <span class="text-capitalize mr-1 font-weight-bold size-20">
              PNR :
            </span>
            <span>{{ reservation?.flights[0].reference }}</span>
          </div>
          <div class="mt-2 d-flex gap-sm primary--text">
            <span class="text-capitalize mr-1 font-weight-bold size-20">
              {{ $t("total") }} :
            </span>
            <p class="mb-0 font-bold">
              <span>{{ reservation?.flights[0].total }} </span>
              <span>{{ reservation?.flights[0].currency }}</span>
            </p>
          </div>
        </div>

        <div>
          <v-row>
            <v-col cols="12">
              <CustomTable v-if="rendering" :tableHeaders="headers" :itemsData="travellers" :fromStore="false"
                :slotColumnNames="[
                  'travelerId',
                  'name',
                  'dateOfBirth',
                  'gender',
                  'email',
                  'phone',
                ]" :showPagination="false">
                <template #item.travelerId="{ item }">
                  {{ item.travelerId }}
                </template>
                <template #item.name="{ item }">
                  <span>{{ item.firstName + " " + item.lastName }}</span>
                </template>
                <template #item.dateOfBirth="{ item }">
                  <span>{{ convertDate(item.dateOfBirth, "LL") }}</span>
                </template>
                <template #item.gender="{ item }">
                  <span>{{ item.gender }}</span>
                </template>

                <template #item.email="{ item }">
                  <span>{{ item.contact_emailAddress }}</span>
                </template>

                <template #item.phone="{ item }">
                  <span class="d-flex" :style="{
                    flexDirection: $vuetify.rtl ? 'row-reverse' : 'row',
                  }">
                    <span>
                      {{ item.contact_phone_countryCallingCode }}
                    </span>
                    <span>{{ item.contact_phone_number }}</span>
                  </span>
                </template>
              </CustomTable>
            </v-col>
          </v-row>
        </div>
        <div class="d-flex justify-end">
          <v-btn @click="openHtmlTicket" class="primary mt-4 text-capitalize text-decoration-none size-20 white--text"
            min-width="200" target="_blank">
            {{ $t("download_ticket") }}
          </v-btn>
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
export default {
  name: "MainData",
  props: {
    reservation: {
      type: Object,
      default: () => { },
    },
    travellers: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      rendering: true,
    };
  },
  computed: {
    headers() {
      const headers = [
        {
          name: "travelerId",
          value: "travelerId",
          showColumn: true,
          width: 25,
        },
        {
          name: "name",
          value: "name",
          sortable: false,
          showColumn: true,
        },
        {
          name: "dateOfBirth",
          value: "dateOfBirth",
          sortable: false,
          showColumn: true,
        },
        {
          name: "gender",
          value: "gender",
          sortable: false,
          showColumn: true,
        },
        {
          name: "email",
          value: "email",
          sortable: false,
          showColumn: true,
        },
        {
          name: "phone",
          value: "phone",
          sortable: false,
          showColumn: true,
        },
      ];
      return headers.filter((head) => head.showColumn);
    },
  },
  components: {},

  methods: {
    openHtmlTicket() {
      const ticketHtml = this.$i18n.locale === 'en'
        ? this.reservation?.ticketEn
        : this.reservation?.ticketAr;

      if (!ticketHtml) {
        alert("التذكرة غير متوفرة.");
        return;
      }
      const blob = new Blob([ticketHtml], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      window.open(url, "_blank");
    }
  }

};

</script>
