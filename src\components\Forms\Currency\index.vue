<template>
  <section>
    <v-btn min-width="20px" text v-if="id" @click="handleShow()">
      <v-icon color="primary"> mdi-pencil</v-icon>
    </v-btn>
    <v-btn @click="dialog = true" color="primary" depressed class="mb-4" v-else>
      {{ $t("new_currency") }}
    </v-btn>
    <CustomDialog :dialog="dialog" @close-dialog="dialog = false">
      <template #header>
        <DialogHeader :title="$t('new_currency')" />
      </template>
      <ValidationObserver ref="form" v-slot="{ handleSubmit }">
        <v-form @submit.prevent="handleSubmit(submitForm)">
          <div class="dialog-form">
            <FormGroup rules="required" name="nameAr" errorName="NameAr">
              <template #default="{ attrs }">
                <InputText
                  v-bind="attrs"
                  v-model="form.nameAr"
                  focus
                  name="nameAr"
                  :label="$t('nameAr')"
                />
              </template>
            </FormGroup>
            <FormGroup rules="required" name="nameEn" errorName="NameEn">
              <template #default="{ attrs }">
                <InputText
                  v-bind="attrs"
                  v-model="form.nameEn"
                  name="nameEn"
                  :label="$t('nameEn')"
                />
              </template>
            </FormGroup>
            <FormGroup rules="required" name="currencyCode" errorName="Code">
              <template #default="{ attrs }">
                <InputText
                  v-bind="attrs"
                  v-model="form.code"
                  name="code"
                  :label="$t('currencyCode')"
                />
              </template>
            </FormGroup>

            <FormGroup rules="required" name="rate" errorName="Rate">
              <template #default="{ attrs }">
                <InputText
                  v-bind="attrs"
                  v-model="form.rate"
                  name="rate"
                  :label="$t('rate')"
                />
              </template>
            </FormGroup>
          </div>
          <div class="d-flex justify-end w-100 gap-md">
            <v-btn
              outlined
              color="primary"
              min-width="120"
              @click="dialog = false"
              >{{ $t("cancel") }}</v-btn
            >
            <v-btn
              class="primary"
              :loading="loading"
              type="submit"
              min-width="120"
              >{{ $t("save") }}</v-btn
            >
          </div>
        </v-form>
      </ValidationObserver>
    </CustomDialog>
  </section>
</template>


<script>
export default {
  name: "CurrencyForm",
  props: ["id", "url"],
  data() {
    return {
      form: {
        nameAr: "",
        nameEn: "",
        code: "",
        rate: null,
      },
      loadingData: false,
      loading: false,
      dialog: false,
    };
  },
  methods: {
    resetForm() {
      this.dialog = false;
      this.$refs.form.reset();
      this.$eventBus.$emit("on-refetch-data");
    },
    async handleShow() {
      this.dialog = true;
      this.loadingData = true;
      const response = await this.$http.get({
        reqName: `${this.url}/get-by-id?Id=${this.id}`,
      });
      this.form = response.data.data;
      this.loadingData = false;
    },
    handleCreate() {
      this.$http
        .post({
          reqName: `${this.url}/create`,
          data: this.form,
        })
        .then(() => {
          this.resetForm();
        })
        .catch((err) => console.log(err))
        .finally(() => (this.loading = false));
    },
    handleUpdate() {
      this.$http
        .put({
          reqName: `${this.url}/update`,
          data: this.form,
        })
        .then(({ data }) => {
          this.form = data.data;
          this.resetForm();
        })
        .catch((err) => console.log(err))
        .finally(() => (this.loading = false));
    },
    submitForm() {
      this.$refs.form.reset();
      this.$refs.form.validate().then((success) => {
        if (success && !this.serverErrors) {
          this.loading = true;
          this.id ? this.handleUpdate() : this.handleCreate();
        }
      });
    },
  },
};
</script>