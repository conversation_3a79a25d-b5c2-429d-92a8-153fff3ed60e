.loader-wrap {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100vh;
  z-index: 9;
  visibility: hidden;
  animation: preloaderWrpper 1.2s ease;
  &__layer {
      &--one {
          position: absolute;
          right: 0;
          top: 0;
          width: 0;
          height: 100%;
          z-index: 9;
          animation: preloaderLayer 1.2s ease;
      }

      &--two {
          position: absolute;
          right: 33.3333%;
          top: 0;
          width: 0;
          height: 100%;
          z-index: 9;
          animation: preloaderLayer 1.2s ease;
      }

      &--three {
          position: absolute;
          right: 66.6666%;
          top: 0;
          width: 0;
          height: 100%;
          z-index: 9;
          animation: preloaderLayer 1.2s ease;
      }

      &__overlay {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          z-index: 9;
          background: var(--primary);
      }
  }

}