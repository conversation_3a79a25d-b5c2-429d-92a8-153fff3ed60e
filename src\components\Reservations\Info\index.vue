<template>
  <v-card class="card mt-8 flight-order">
    <v-card-text>
      <div v-for="(flight, index) in flightDetails" :key="index">
        <div class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <h5>
              {{
                flight.isDeparture ? $t("going_flight") : $t("return_flight")
              }}
            </h5>
          </div>
          <div style="color: #85979e" class="size-14">
            <span> {{ flight.carrierCode }} | </span>
            <span>{{ $t("flight_no") }} {{ flight.number }}</span>
          </div>
        </div>
        <FlightsTravelInfoItem :travel="flight" class="travel--details" />
      </div>
    </v-card-text>
  </v-card>
</template>
<script>
import FlightsTravelInfoItem from "./Item";
export default {
  name: "FlightsTravelInfo",
  props: {
    flightDetails: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    FlightsTravelInfoItem,
  },
};
</script>
