<template>
    <div class="loader-wrap">
        <div class="loader-wrap__layer loader-wrap__layer--one">
            <span class="loader-wrap__layer__overlay"></span>
        </div>
        <div class="loader-wrap__layer loader-wrap__layer--two">
            <span class="loader-wrap__layer__overlay"></span>
        </div>
        <div class="loader-wrap__layer loader-wrap__layer--three">
            <span class="loader-wrap__layer__overlay"></span>
        </div>
    </div>
</template>
<script>
export default {
    name: "Preloader"
}
</script>

<style lang="scss">
@import "index.scss";
</style>
