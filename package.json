{"name": "<PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"serve": "NODE_ENV=dev vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint --fix", "build-dev": "NODE_ENV=dev vue-cli-service build", "build-test": "NODE_ENV=testing vue-cli-service build", "build-prod": "NODE_ENV=production vue-cli-service build", "serve-dev": "NODE_ENV=dev vue-cli-service serve --port=40003", "serve-test": "NODE_ENV=testing vue-cli-service serve --port=5023", "serve-prod": "NODE_ENV=production vue-cli-service serve --port=30003"}, "dependencies": {"axios": "^0.27.2", "core-js": "^3.8.3", "firebase": "^9.8.2", "js-cookie": "^3.0.1", "moment": "^2.30.1", "nprogress": "^0.2.0", "vee-validate": "^3.4.14", "vue": "^2.6.14", "vue-html2pdf": "^1.8.0", "vue-i18n": "^8.27.2", "vue-moment": "^4.1.0", "vue-router": "^3.5.1", "vue-toasted": "^1.1.28", "vue-toastification": "^1.7.14", "vue2-editor": "^2.10.3", "vuetify": "^2.6.0", "vuetify-datetime-picker": "^2.1.1", "vuex": "^3.6.2", "win-node-env": "^0.6.1"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "node-sass": "^7.0.1", "sass": "^1.54.5", "sass-loader": "^12.0.0", "vue-cli-plugin-vuetify": "~2.5.4", "vue-template-compiler": "^2.6.14", "vuetify-loader": "^1.7.0"}}