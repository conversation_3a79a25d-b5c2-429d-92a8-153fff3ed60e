export const usersRoutes = {
  path: "/users",
  to: "/users",
  icon: "mdi-account",
  activeRoutes: ["users_record"],
  meta: {
    title: "users",
  },
  showInMenu: true,
  order: 5,
  component: () => import("@/views/Users"),
  children: [
    {
      path: "/",
      component: () => import("@/views/Users/<USER>"),
      name: "users_record",
      icon: "mdi-file-document-multiple-outline",
      to: "/users",
      allowed: false,
      meta: {
        title: "users_record",

      }
    },
  ]
};
