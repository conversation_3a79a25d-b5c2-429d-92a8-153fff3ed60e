<template>
  <main class="mt-4">
    <v-container>
      <v-card class="page-card">
        <v-card-text>
          <v-row>
            <v-col cols="12">
              <CustomTable
                v-if="rendering"
                :tableHeaders="headers"
                :reqName="`${url}/paginated?GetActiveOnly=false`"
                :slotColumnNames="['#', 'image', 'isActive', 'actions']"
                :title="$t('carousel')"
              >
                <template #top-right>
                  <CarouselForm :url="url" />
                </template>
                <template #item.#="{ item, index }">
                  {{ index + pagination.from }}
                </template>
                <template #item.image="{ item }">
                  <v-img :src="item.image" width="60" height="60"></v-img>
                </template>
                <template #item.isActive="{ item }">
                  <toggle-service
                    :is-edit="true"
                    :model-name="`${url}/activate-deactivate`"
                    :model-id="item.id"
                    v-model="item.isActive"
                  />
                </template>
                <template #item.actions="{ item }">
                  <div class="d-flex align-center justify-center">
                    <CarouselForm :url="url" :id="item.id" />
                    <ImageDialog
                      :image="item.image"
                      url="carusal/add-image"
                      deleteUrl="carusal/delete-image"
                      :hasDeleteOption="true"
                      :id="item.id"
                      @update-table="updateTable"
                    />
                  </div>
                </template>
              </CustomTable>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-container>
  </main>
</template>



<script>
import update from "@/mixins/update"
import CarouselForm from "@/components/Forms/Carousel";
export default {
  name: "CarouselRecord",
  components: {
    CarouselForm,
  },
  mixins: [update],

  data() {
    return {
      rendering: true,
      url: "carusal",
    };
  },
  computed: {
    headers() {
      const headers = [
        {
          name: "#",
          value: "#",
          showColumn: true,
        },
        {
          name: "name",
          value: "name",
          sortable: false,
          showColumn: true,
        },
        {
          name: "image",
          value: "image",
          sortable: false,
          showColumn: true,
        },
        {
          name: "description",
          value: "description",
          sortable: false,
          showColumn: true,
        },
        {
          name: "orderPriority",
          value: "orderPriority",
          sortable: false,
          showColumn: true,
        },
        {
          name: "active",
          value: "isActive",
          sortable: false,
          showColumn: true,
        },

        {
          name: "actions",
          value: "actions",
          showColumn: true,
        },
      ];
      return headers.filter((head) => head.showColumn);
    },
  },
};
</script>