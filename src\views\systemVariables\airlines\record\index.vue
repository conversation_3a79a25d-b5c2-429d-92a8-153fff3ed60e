<template>
  <main class="mt-4">
    <v-container>
      <v-card class="page-card">
        <v-card-text>
          <v-row>
            <v-col cols="12">
              <CustomTable
                v-if="rendering"
                :tableHeaders="headers"
                :reqName="`${url}/paginated?GetActiveOnly=false`"
                :slotColumnNames="[
                  '#',
                  'isActive',
                  'actions',
                  'airlineLogo',
                  'isIncluded',
                ]"
                :title="$t('airlines')"
              >
                <template #top-right>
                  <AirlineForm :url="url" />
                </template>
                <template #top-left>
                  <InputText
                    v-model="search"
                    name="search"
                    hide-details
                    outlined
                    class="mx-4"
                    dense
                    clearable
                    :label="$t('search')"
                    @input="setSearchQuery"
                  >
                  </InputText>
                </template>
                <template #item.#="{ item, index }">
                  {{ index + pagination.from }}
                </template>
                <template #item.isActive="{ item }">
                  <toggle-service
                    :is-edit="true"
                    :model-name="`${url}/activate-deactivate`"
                    :model-id="item.id"
                    v-model="item.isActive"
                  />
                </template>
                <template #item.isIncluded="{ item }">
                  <toggle-service
                    :is-edit="true"
                    :model-name="`${url}/Inculded-Exculsive`"
                    :model-id="item.id"
                    v-model="item.isIncluded"
                  />
                </template>
                <template #item.airlineLogo="{ item }">
                  <v-img :src="item.airlineLogo" alt="airline " width="60" />
                </template>
                <template #item.actions="{ item }">
                  <td class="d-flex align-center justify-center">
                    <AirlineForm :url="url" :id="item.id" />
                    <ImageDialog
                      :image="item.image"
                      url="airline/add-image"
                      deleteUrl="airline/delete-image"
                      :hasDeleteOption="true"
                      :id="item.id"
                      @update-table="updateTable"
                    />
                  </td>
                </template>
              </CustomTable>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-container>
  </main>
</template>



<script>
import AirlineForm from "@/components/Forms/Airline";

import { mapActions } from "vuex";

export default {
  name: "AirlineRecord",
  components: {
    AirlineForm,
  },
  data() {
    return {
      rendering: true,
      url: "airline",
      search: "",
    };
  },
  computed: {
    headers() {
      const headers = [
        {
          name: "#",
          value: "#",
          showColumn: true,
        },
        {
          name: "airlineLogo",
          value: "airlineLogo",
          sortable: false,
          showColumn: true,
        },
        {
          name: "name",
          value: "name",
          sortable: false,
          showColumn: true,
        },
        {
          name: "init",
          value: "init",
          sortable: false,
          showColumn: true,
        },
        // {
        //   name: "icaoCode",
        //   value: "icaoCode",
        //   sortable: false,
        //   showColumn: true,
        // },

        {
          name: "active",
          value: "isActive",
          sortable: false,
          showColumn: true,
        },
        {
          name: "is_included",
          value: "isIncluded",
          sortable: false,
          showColumn: true,
        },

        {
          name: "actions",
          value: "actions",
          showColumn: true,
        },
      ];
      return headers.filter((head) => head.showColumn);
    },
  },
  methods: {
    ...mapActions(["setQuery"]),
    setSearchQuery() {
      console.log(this.search);
      let query = {
        SearchString: this.search,
      };
      this.setQuery(query);
      // this.rendering = false;
      // setTimeout(() => {
      //   this.rendering = true;
      // }, 10);
    },
  },
};
</script>