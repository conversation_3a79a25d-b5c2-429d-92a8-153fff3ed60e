<template>
  <div class="relative">
    <label>
      <strong class="font-size-14">{{ $t(label) }}</strong>
    </label>
    <v-otp-input length="6" v-bind="$attrs" v-on="$listeners"
      @input="clearServerErrors"
    ></v-otp-input>
    <v-overlay absolute :value="loading">
      <v-progress-circular indeterminate color="primary"></v-progress-circular>
    </v-overlay>
  </div>
</template>


<script>
export default {
  name: "OtpInput",
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    label: {
      type: String,
      default: "",
    },
  },
  methods: {
    clearServerErrors() {
      if (this.serverErrors) {
        this.$store.dispatch("ClearServerErrors");
      }
    }
  }
};
</script>