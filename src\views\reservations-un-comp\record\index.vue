<template>
  <main class="mt-4">
    <v-container>
      <!-- Statistics Cards -->
      <v-row class="mb-4">
        <v-col cols="12" sm="6" md="3">
          <v-card class="pa-4 text-center stats-card" color="warning" dark elevation="3">
            <v-icon size="40" class="mb-2">mdi-clock-outline</v-icon>
            <h2 class="display-1 font-weight-bold">{{ stats.pending }}</h2>
            <p class="mb-0 subtitle-1">قيد الانتظار</p>
          </v-card>
        </v-col>
        <v-col cols="12" sm="6" md="3">
          <v-card class="pa-4 text-center stats-card" color="error" dark elevation="3">
            <v-icon size="40" class="mb-2">mdi-cancel</v-icon>
            <h2 class="display-1 font-weight-bold">{{ stats.cancelled }}</h2>
            <p class="mb-0 subtitle-1">ملغية</p>
          </v-card>
        </v-col>
        <v-col cols="12" sm="6" md="3">
          <v-card class="pa-4 text-center stats-card" color="info" dark elevation="3">
            <v-icon size="40" class="mb-2">mdi-cash-refund</v-icon>
            <h2 class="display-1 font-weight-bold">{{ stats.refunded }}</h2>
            <p class="mb-0 subtitle-1">مستردة</p>
          </v-card>
        </v-col>
        <v-col cols="12" sm="6" md="3">
          <v-card class="pa-4 text-center stats-card" color="primary" dark elevation="3">
            <v-icon size="40" class="mb-2">mdi-file-document-multiple</v-icon>
            <h2 class="display-1 font-weight-bold">{{ stats.total }}</h2>
            <p class="mb-0 subtitle-1">المجموع</p>
          </v-card>
        </v-col>
      </v-row>

      <v-card class="page-card">
        <v-card-text class="pa-2">
          <v-row no-gutters>
            <v-col cols="12">
              <CustomTable
                v-if="rendering"
                :tableHeaders="headers"
                reqName="reservation/paginated?IsCompleted=false"
                :slotColumnNames="[
                  '#',
                  'creationDate',
                  'from',
                  'fromDate',
                  'to',
                  'toDate',
                  'pnr',
                  'isActive',
                  'isPaid',
                  'isRefunded',
                  'status'
                ]"
                :title="$t('reservations-un-comp')"
                @handleClick="handleClick"
                :isClickable="true"
                class="full-width-table"
              >
                <template #top-left>
                  <div class="d-flex gap-3 align-center flex-wrap">
                    <InputText
                      v-model="search"
                      name="search"
                      hide-details
                      outlined
                      dense
                      clearable
                      :label="$t('search')"
                      @input="setSearchQuery"
                      style="min-width: 200px;"
                    >
                    </InputText>

                    <v-select
                      v-model="statusFilter"
                      :items="statusOptions"
                      :label="$t('filter_by_status')"
                      outlined
                      dense
                      hide-details
                      clearable
                      @change="applyStatusFilter"
                      style="min-width: 150px;"
                    ></v-select>

                    <v-menu
                      v-model="dateMenu"
                      :close-on-content-click="false"
                      :nudge-right="40"
                      transition="scale-transition"
                      offset-y
                      min-width="auto"
                    >
                      <template v-slot:activator="{ on, attrs }">
                        <v-text-field
                          v-model="dateRange"
                          :label="$t('filter_by_date')"
                          prepend-icon="mdi-calendar"
                          readonly
                          outlined
                          dense
                          hide-details
                          v-bind="attrs"
                          v-on="on"
                          style="min-width: 200px;"
                        ></v-text-field>
                      </template>
                      <v-date-picker
                        v-model="selectedDates"
                        range
                        @change="applyDateFilter"
                      ></v-date-picker>
                    </v-menu>
                  </div>
                </template>
                <template #item.#="{ item, index }">
                  {{ index + pagination.from }}
                </template>
                <template #item.creationDate="{ item }">
                  <span>{{
                    convertDate(item.flights[0].creationDate, "LL HH:mm")
                  }}</span>
                </template>
                <template #item.from="{ item }">
                  <span>{{
                    item.flights[0].flightDetails[0].departure_iataCode
                  }}</span>
                </template>
                <template #item.fromDate="{ item }">
                  <span>{{
                    convertDate(
                      item.flights[0].flightDetails[0].departure_at,
                      "LL HH:mm"
                    )
                  }}</span>
                </template>

                <template #item.to="{ item }">
                  <span>{{
                    item.flights[0].flightDetails[
                      item.flights[0].flightDetails.length - 1
                    ].arrival_iataCode
                  }}</span>
                </template>

                <template #item.toDate="{ item }">
                  <span>{{
                    convertDate(
                      item.flights[0].flightDetails[
                        item.flights[0].flightDetails.length - 1
                      ].arrival_at,
                      "LL HH:mm"
                    )
                  }}</span>
                </template>
                <template #item.pnr="{ item }">
                  <span>{{item.flights[0].reference }}</span>
                </template>
                <!--
                <template #item.ticketEn="{ item }">
                  <v-btn
                    :href="item.ticketEn"
                    target="_blank"
                    text
                    color="primary"
                  >
                    <v-icon>mdi-ticket</v-icon>
                  </v-btn>
                </template>
                <template #item.#="{ item, index }">
                  {{ index + pagination.from }}
                </template> -->
                <template #item.isActive="{ item }">
            <v-chip
              :color="item.isActive ? 'success' : 'error'"
              text-color="white"
              small
              class="font-weight-bold"
            >
              {{item.isActive ? "نشطة" : "غير نشطة"}}
            </v-chip>
          </template>
          <template #item.isPaid="{ item }">
            <v-chip
              :color="item.isPaid ? 'success' : 'orange'"
              text-color="white"
              small
              class="font-weight-bold"
            >
              {{item.isPaid ? "مدفوعة" : "غير مدفوعة"}}
            </v-chip>
          </template>
          <template #item.isRefunded="{ item }">
            <v-chip
              :color="item.isRefunded ? 'cyan' : 'grey'"
              text-color="white"
              small
              class="font-weight-bold"
            >
              {{item.isRefunded ? "تم الاسترداد" : "لم يتم الاسترداد"}}
            </v-chip>
          </template>

          <template #item.status="{ item }">
            <v-chip
              :color="getStatusColor(item)"
              text-color="white"
              small
            >
              {{ getStatusText(item) }}
            </v-chip>
          </template>



              </CustomTable>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-container>
  </main>
</template>

<script>
import { mapActions } from "vuex";
export default {
  name: "ReservationsRecord",
  data() {
    return {
      rendering: true,
      search: "",
      statusFilter: null,
      dateMenu: false,
      selectedDates: [],
      dateRange: "",
      stats: {
        pending: 0,
        cancelled: 0,
        refunded: 0,
        total: 0
      },
      statusOptions: [
        { text: 'قيد الانتظار', value: 'pending' },
        { text: 'ملغية', value: 'cancelled' },
        { text: 'مستردة', value: 'refunded' },
        { text: 'نشطة', value: 'active' }
      ]
    };
  },
  computed: {
    headers() {
      const headers = [
        {
          name: "#",
          value: "#",
          showColumn: true,
          width: 25,
        },
        {
          name: "creationDate",
          value: "creationDate",
          sortable: false,
          showColumn: true,
        },
        {
          name: "from",
          value: "from",
          sortable: false,
          showColumn: true,
        },
        {
          name: "fromDate",
          value: "fromDate",
          sortable: false,
          showColumn: true,
        },
        {
          name: "to",
          value: "to",
          sortable: false,
          showColumn: true,
        },
        {
          name: "toDate",
          value: "toDate",
          sortable: false,
          showColumn: true,
        },
        {
          name: "pnr",
          value: "pnr",
          sortable: false,
          showColumn: true,
        },
        {
          name: "نشطة",
          value: "isActive",
          sortable: false,
          showColumn: true,
        },
        {
          name: "مدفوعة",
          value: "isPaid",
          sortable: false,
          showColumn: true,
        },
        {
          name: "تم استردادها",
          value: "isRefunded",
          sortable: false,
          showColumn: true,
        },
        {
          name: "الحالة",
          value: "status",
          sortable: false,
          showColumn: true,
        }

      ];
      return headers.filter((head) => head.showColumn);
    },
  },
  methods: {
    ...mapActions(["setQuery"]),
    setSearchQuery() {
      console.log(this.search);
      let query = {
        SearchString: this.search,
      };
      this.setQuery(query);
      // this.rendering = false;
      // setTimeout(() => {
      //   this.rendering = true;
      // }, 10);
    },
    handleClick(item) {
      this.$router.push(`/reservations-un-comp/${item.reservationGUID}`);
    },

    getStatusColor(item) {
      if (!item.isActive) return 'error';
      if (item.isPaid) return 'success';
      if (item.isRefunded) return 'info';
      return 'warning';
    },

    getStatusText(item) {
      if (!item.isActive) return 'ملغية';
      if (item.isPaid) return 'مكتملة';
      if (item.isRefunded) return 'مستردة';
      return 'قيد الانتظار';
    },



    getTableData() {
      this.rendering = false;
      this.$nextTick(() => {
        this.rendering = true;
      });
    },

    applyStatusFilter() {
      let query = {
        SearchString: this.search,
      };

      if (this.statusFilter) {
        switch(this.statusFilter) {
          case 'pending':
            query.IsActive = true;
            query.IsPaid = false;
            break;
          case 'cancelled':
            query.IsActive = false;
            break;
          case 'refunded':
            query.IsRefunded = true;
            break;
          case 'active':
            query.IsActive = true;
            break;
        }
      }

      this.setQuery(query);
    },

    applyDateFilter() {
      if (this.selectedDates && this.selectedDates.length === 2) {
        this.dateRange = `${this.selectedDates[0]} - ${this.selectedDates[1]}`;
        let query = {
          SearchString: this.search,
          FromDate: this.selectedDates[0],
          ToDate: this.selectedDates[1]
        };
        this.setQuery(query);
        this.dateMenu = false;
      }
    },

    calculateStats() {
      // يمكن استبدال هذا بـ API call حقيقي لاحقاً
      this.$http.get({
        reqName: 'reservation/stats?IsCompleted=false'
      }).then(({ data: response }) => {
        this.stats = response.data || {
          pending: 0,
          cancelled: 0,
          refunded: 0,
          total: 0
        };
      }).catch(() => {
        // في حالة فشل الـ API، استخدم قيم افتراضية
        this.stats = {
          pending: 25,
          cancelled: 8,
          refunded: 12,
          total: 45
        };
      });
    },
  },

  mounted() {
    this.calculateStats();
  },
};
</script>

<style scoped>
.full-width-table {
  width: 100% !important;
  overflow-x: auto !important;
}

.full-width-table >>> .v-data-table {
  width: 100% !important;
  min-width: 100% !important;
}

.full-width-table >>> .v-data-table__wrapper {
  overflow-x: auto !important;
  width: 100% !important;
}

.full-width-table >>> .v-data-table thead th {
  white-space: nowrap !important;
  min-width: 120px !important;
}

.full-width-table >>> .v-data-table tbody td {
  white-space: nowrap !important;
  min-width: 120px !important;
}

/* Make table responsive */
@media (max-width: 768px) {
  .full-width-table >>> .v-data-table thead th {
    min-width: 100px !important;
    font-size: 12px !important;
  }

  .full-width-table >>> .v-data-table tbody td {
    min-width: 100px !important;
    font-size: 12px !important;
  }
}

/* Ensure container takes full width */
.page-card {
  width: 100% !important;
  overflow-x: auto !important;
  margin: 0 !important;
}

.v-container {
  max-width: 100% !important;
  padding: 12px !important;
  margin: 0 !important;
}

/* Remove any fixed widths and allow table to expand */
.full-width-table >>> .v-data-table {
  table-layout: auto !important;
}

.full-width-table >>> .v-data-table th,
.full-width-table >>> .v-data-table td {
  padding: 8px 12px !important;
}

/* Specific column widths */
.full-width-table >>> .v-data-table th:first-child,
.full-width-table >>> .v-data-table td:first-child {
  min-width: 60px !important;
  max-width: 60px !important;
}

/* Status chips styling */
.full-width-table >>> .v-chip {
  font-size: 11px !important;
  height: 24px !important;
}

/* Statistics cards styling */
.stats-card {
  transition: transform 0.2s ease-in-out !important;
  border-radius: 12px !important;
}

.stats-card:hover {
  transform: translateY(-2px) !important;
}

.stats-card .v-icon {
  opacity: 0.9;
}

.stats-card h2 {
  margin: 8px 0 !important;
}

/* Improve filter section */
.d-flex.gap-3 > * {
  margin-left: 8px !important;
}

.d-flex.gap-3 > *:first-child {
  margin-left: 0 !important;
}
</style>
