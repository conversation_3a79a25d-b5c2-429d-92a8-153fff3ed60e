<template>
  <main class="mt-4">
    <v-container>
      <!-- Statistics Cards -->
      <v-row class="mb-4">
        <v-col cols="12" sm="6" md="3">
          <v-card class="pa-4 text-center" color="warning" dark>
            <v-icon size="40" class="mb-2">mdi-clock-outline</v-icon>
            <h3>{{ stats.pending }}</h3>
            <p class="mb-0">قيد الانتظار</p>
          </v-card>
        </v-col>
        <v-col cols="12" sm="6" md="3">
          <v-card class="pa-4 text-center" color="error" dark>
            <v-icon size="40" class="mb-2">mdi-cancel</v-icon>
            <h3>{{ stats.cancelled }}</h3>
            <p class="mb-0">ملغية</p>
          </v-card>
        </v-col>
        <v-col cols="12" sm="6" md="3">
          <v-card class="pa-4 text-center" color="info" dark>
            <v-icon size="40" class="mb-2">mdi-cash-refund</v-icon>
            <h3>{{ stats.refunded }}</h3>
            <p class="mb-0">مستردة</p>
          </v-card>
        </v-col>
        <v-col cols="12" sm="6" md="3">
          <v-card class="pa-4 text-center" color="primary" dark>
            <v-icon size="40" class="mb-2">mdi-file-document-multiple</v-icon>
            <h3>{{ stats.total }}</h3>
            <p class="mb-0">المجموع</p>
          </v-card>
        </v-col>
      </v-row>

      <v-card class="page-card">
        <v-card-text>
          <v-row>
            <v-col cols="12">
              <CustomTable
                v-if="rendering"
                :tableHeaders="headers"
                reqName="reservation/paginated?IsCompleted=false"
                :slotColumnNames="[
                  '#',
                  'creationDate',
                  'from',
                  'fromDate',
                  'to',
                  'toDate',
                  'pnr',
                  'isActive',
                  'isPaid',
                  'isRefunded',
                  'status',
                  'actions'
                ]"
                :title="$t('reservations-un-comp')"
                @handleClick="handleClick"
                :isClickable="true"
              >
                <template #top-left>
                  <div class="d-flex gap-3 align-center flex-wrap">
                    <InputText
                      v-model="search"
                      name="search"
                      hide-details
                      outlined
                      dense
                      clearable
                      :label="$t('search')"
                      @input="setSearchQuery"
                      style="min-width: 200px;"
                    >
                    </InputText>

                    <v-select
                      v-model="statusFilter"
                      :items="statusOptions"
                      :label="$t('filter_by_status')"
                      outlined
                      dense
                      hide-details
                      clearable
                      @change="applyStatusFilter"
                      style="min-width: 150px;"
                    ></v-select>

                    <v-menu
                      v-model="dateMenu"
                      :close-on-content-click="false"
                      :nudge-right="40"
                      transition="scale-transition"
                      offset-y
                      min-width="auto"
                    >
                      <template v-slot:activator="{ on, attrs }">
                        <v-text-field
                          v-model="dateRange"
                          :label="$t('filter_by_date')"
                          prepend-icon="mdi-calendar"
                          readonly
                          outlined
                          dense
                          hide-details
                          v-bind="attrs"
                          v-on="on"
                          style="min-width: 200px;"
                        ></v-text-field>
                      </template>
                      <v-date-picker
                        v-model="selectedDates"
                        range
                        @change="applyDateFilter"
                      ></v-date-picker>
                    </v-menu>
                  </div>
                </template>
                <template #item.#="{ item, index }">
                  {{ index + pagination.from }}
                </template>
                <template #item.creationDate="{ item }">
                  <span>{{
                    convertDate(item.flights[0].creationDate, "LL HH:mm")
                  }}</span>
                </template>
                <template #item.from="{ item }">
                  <span>{{
                    item.flights[0].flightDetails[0].departure_iataCode
                  }}</span>
                </template>
                <template #item.fromDate="{ item }">
                  <span>{{
                    convertDate(
                      item.flights[0].flightDetails[0].departure_at,
                      "LL HH:mm"
                    )
                  }}</span>
                </template>

                <template #item.to="{ item }">
                  <span>{{
                    item.flights[0].flightDetails[
                      item.flights[0].flightDetails.length - 1
                    ].arrival_iataCode
                  }}</span>
                </template>

                <template #item.toDate="{ item }">
                  <span>{{
                    convertDate(
                      item.flights[0].flightDetails[
                        item.flights[0].flightDetails.length - 1
                      ].arrival_at,
                      "LL HH:mm"
                    )
                  }}</span>
                </template>
                <template #item.pnr="{ item }">
                  <span>{{item.flights[0].reference }}</span>
                </template>
                <!--
                <template #item.ticketEn="{ item }">
                  <v-btn
                    :href="item.ticketEn"
                    target="_blank"
                    text
                    color="primary"
                  >
                    <v-icon>mdi-ticket</v-icon>
                  </v-btn>
                </template>
                <template #item.#="{ item, index }">
                  {{ index + pagination.from }}
                </template> -->
                <template #item.isActive="{ item }">
            <v-chip
              :color="item.isActive ? 'success' : 'error'"
              text-color="white"
              small
            >
              {{item.isActive ? "نشطة" : "غير نشطة"}}
            </v-chip>
          </template>
          <template #item.isPaid="{ item }">
            <v-chip
              :color="item.isPaid ? 'success' : 'warning'"
              text-color="white"
              small
            >
              {{item.isPaid ? "مدفوعة" : "غير مدفوعة"}}
            </v-chip>
          </template>
          <template #item.isRefunded="{ item }">
            <v-chip
              :color="item.isRefunded ? 'info' : 'grey'"
              text-color="white"
              small
            >
              {{item.isRefunded ? "تم الاسترداد" : "لم يتم الاسترداد"}}
            </v-chip>
          </template>

          <template #item.status="{ item }">
            <v-chip
              :color="getStatusColor(item)"
              text-color="white"
              small
            >
              {{ getStatusText(item) }}
            </v-chip>
          </template>

          <template #item.actions="{ item }">
            <div class="d-flex gap-2">
              <v-btn
                icon
                small
                color="primary"
                @click.stop="viewDetails(item)"
                :title="$t('view_details')"
              >
                <v-icon small>mdi-eye</v-icon>
              </v-btn>
              <v-btn
                icon
                small
                color="success"
                @click.stop="completeReservation(item)"
                :title="$t('complete_reservation')"
                v-if="!item.isPaid"
              >
                <v-icon small>mdi-check-circle</v-icon>
              </v-btn>
              <v-btn
                icon
                small
                color="error"
                @click.stop="cancelReservation(item)"
                :title="$t('cancel_reservation')"
                v-if="item.isActive"
              >
                <v-icon small>mdi-cancel</v-icon>
              </v-btn>
            </div>
          </template>

              </CustomTable>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-container>
  </main>
</template>

<script>
import { mapActions } from "vuex";
export default {
  name: "ReservationsRecord",
  data() {
    return {
      rendering: true,
      search: "",
    };
  },
  computed: {
    headers() {
      const headers = [
        {
          name: "#",
          value: "#",
          showColumn: true,
          width: 25,
        },
        {
          name: "creationDate",
          value: "creationDate",
          sortable: false,
          showColumn: true,
        },
        {
          name: "from",
          value: "from",
          sortable: false,
          showColumn: true,
        },
        {
          name: "fromDate",
          value: "fromDate",
          sortable: false,
          showColumn: true,
        },
        {
          name: "to",
          value: "to",
          sortable: false,
          showColumn: true,
        },
        {
          name: "toDate",
          value: "toDate",
          sortable: false,
          showColumn: true,
        },
        {
          name: "pnr",
          value: "pnr",
          sortable: false,
          showColumn: true,
        },
        {
          name: "نشطة",
          value: "isActive",
          sortable: false,
          showColumn: true,
        },
        {
          name: "مدفوعة",
          value: "isPaid",
          sortable: false,
          showColumn: true,
        },
        {
          name: "تم استردادها",
          value: "isRefunded",
          sortable: false,
          showColumn: true,
        },
        {
          name: "الحالة",
          value: "status",
          sortable: false,
          showColumn: true,
        },
        {
          name: "الإجراءات",
          value: "actions",
          sortable: false,
          showColumn: true,
          width: 150,
        }

      ];
      return headers.filter((head) => head.showColumn);
    },
  },
  methods: {
    ...mapActions(["setQuery"]),
    setSearchQuery() {
      console.log(this.search);
      let query = {
        SearchString: this.search,
      };
      this.setQuery(query);
      // this.rendering = false;
      // setTimeout(() => {
      //   this.rendering = true;
      // }, 10);
    },
    handleClick(item) {
      this.$router.push(`/reservations-un-comp/${item.reservationGUID}`);
    },

    getStatusColor(item) {
      if (!item.isActive) return 'error';
      if (item.isPaid) return 'success';
      if (item.isRefunded) return 'info';
      return 'warning';
    },

    getStatusText(item) {
      if (!item.isActive) return 'ملغية';
      if (item.isPaid) return 'مكتملة';
      if (item.isRefunded) return 'مستردة';
      return 'قيد الانتظار';
    },

    viewDetails(item) {
      this.$router.push(`/reservations-un-comp/${item.reservationGUID}`);
    },

    completeReservation(item) {
      this.$confirm({
        title: 'تأكيد إكمال الحجز',
        message: 'هل أنت متأكد من إكمال هذا الحجز؟',
        confirmText: 'نعم، أكمل',
        cancelText: 'إلغاء'
      }).then(() => {
        this.$http.put({
          reqName: `reservation/complete/${item.reservationGUID}`
        }).then(() => {
          this.$toast.success('تم إكمال الحجز بنجاح');
          this.getTableData();
        }).catch(error => {
          this.$toast.error('حدث خطأ أثناء إكمال الحجز');
          console.error(error);
        });
      });
    },

    cancelReservation(item) {
      this.$confirm({
        title: 'تأكيد إلغاء الحجز',
        message: 'هل أنت متأكد من إلغاء هذا الحجز؟',
        confirmText: 'نعم، ألغي',
        cancelText: 'إلغاء'
      }).then(() => {
        this.$http.put({
          reqName: `reservation/cancel/${item.reservationGUID}`
        }).then(() => {
          this.$toast.success('تم إلغاء الحجز بنجاح');
          this.getTableData();
        }).catch(error => {
          this.$toast.error('حدث خطأ أثناء إلغاء الحجز');
          console.error(error);
        });
      });
    },

    getTableData() {
      this.rendering = false;
      this.$nextTick(() => {
        this.rendering = true;
      });
    },
  },
};
</script>
