body{

  .v-application .red,
  .v-application .error {
      background-color: #fae1e4 !important;
      border: .5px solid #d02c47 !important;
      * {
          color: #d02c47 !important;
      }
  }
  
  .v-application .success {
      background-color: #f1fff2 !important;
      border: .5px solid #4bae4f !important;
      color: #4bae4f !important;
  }

  .v-card{
    box-shadow: inset 0 0 6px rgba(13,164,135,0.1) !important;
    // background-color: #f5f5f5 !important;
    border-radius: 5px !important;
  }
}
.v-list-item::before{
  display: none !important;
}



.v-data-table > .v-data-table__wrapper > table > thead > tr > th{
  color: unset !important;
}