export const reservationsRoutes = {
  path: "/reservations",
  to: "/reservations",
  icon: "mdi-file-document-multiple-outline",
  activeRoutes: ["reservations_record"],
  meta: {
    title: "reservations",
  },
  showInMenu: true,
  order: 7,
  component: () => import("@/views/reservations"),
  children: [
    {
      path: "/",
      component: () => import("@/views/reservations/record"),
      name: "reservations_record",
      icon: "mdi-file-document-multiple-outline",
      to: "/reservations",
      allowed: false,
      meta: {
        title: "reservations_record",
      },
    },
    {
      path: "/reservations/:reservationGUID",
      name: "ReservationDetails",
      component: () => import("@/views/reservations/reservationDetails"),
    },
  ],
};
