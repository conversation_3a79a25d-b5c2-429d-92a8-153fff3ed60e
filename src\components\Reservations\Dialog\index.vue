<template>
  <v-dialog
    max-width="1200"
    v-bind="$attrs"
    @click:outside="$emit('close-dialog')"
  >
    <v-card>
      <v-container>
        <div class="d-flex align-center justify-space-between">
          <h1>{{ $t("reservationDetails") }}</h1>

          <v-btn icon @click="$emit('close-dialog')" depressed color="primary">
            <v-icon size="24">mdi-close</v-icon>
          </v-btn>
        </div>

        <TabsHorizontal :tabs="items">
          <template>
            <v-tab-item value="tab-0">
              <MainData :reservation="reservation" :travellers="travelers" />
              <ResevationsInfo
                :flightDetails="reservation?.flights[0]?.flightDetails"
              />
            </v-tab-item>
            <v-tab-item value="tab-1">
              <PaymentInfo :payments="reservation?.payments" />
            </v-tab-item>
          </template>
        </TabsHorizontal>
      </v-container>
    </v-card>
  </v-dialog>
</template>

<script>
import TabsHorizontal from "@/components/Tabs/Horizontal";
import MainData from "@/components/Reservations/MainData";
import PaymentInfo from "@/components/Reservations/payment";
import ResevationsInfo from "@/components/Reservations/Info";
export default {
  name: "ReservationDetails",
  components: {
    MainData,
    ResevationsInfo,
    PaymentInfo,
    TabsHorizontal,
  },
  props: ["reservationGUID"],
  data() {
    return {
      tab: null,
      reservation: null,
      loading: false,
      travelers: [],

      items: [
        {
          title: this.$t("main_data"),
          name: "MainData",
        },
        {
          title: this.$t("payment_info"),
          name: "PaymentInfo",
        },
        // {
        //   title: this.$t("travellers"),
        //   name: "Travellers",
        //   props: { travellers: this.travelers },
        // },
      ],
    };
  },

  mounted() {
    this.getResevations();
  },

  methods: {
    getResevations() {
      this.loading = true;
      this.$http
        .get({
          reqName: `/reservation/get-by-id?ReservationGUID=${this.reservationGUID}`,
        })
        .then(({ data: response }) => {
          console.log(response.data);
          this.reservation = response.data;
          this.travelers = response.data.travelers;
          this.loading = false;
        });
    },
  },
};
</script>
