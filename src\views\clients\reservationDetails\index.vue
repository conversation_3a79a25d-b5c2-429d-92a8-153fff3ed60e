<template>
  <div class="mt-2">
    <v-container>
      <TabsHorizontal :tabs="items">
        <template>
          <v-tab-item value="tab-0">
            <UserInfo
              :userData="userData"
              :reservations="reservations"
            />

          </v-tab-item>
        </template>
      </TabsHorizontal>
    </v-container>
  </div>
</template>

<script>
import TabsHorizontal from "@/components/Tabs/Horizontal";
import UserInfo from "@/components/UserInfo";
export default {
  name: "ReservationDetails",
  components: {
    UserInfo,
    TabsHorizontal,
  },
  data() {
    return {
      userData: {},
      tab: null,
      reservations: [],
      loading: false,
      travelers: [],

      items: [
        {
          title: this.$t("main_data"),
          name: "MainData",
        },
      ],
    };
  },
  computed: {
    userID() {
      return this.$route.params.userID;
    },
  },

  created() {
    Promise.all([this.getUser(), this.getResevations()]);
  },

  methods: {
    getUser() {
      this.$http
        .get({
          reqName: `/user/get-by-id?id=${this.userID}`,
        })
        .then(({ data: response }) => {
          console.log(response.data);
          this.userData = response.data;
        });
    },
    getResevations() {
      this.loading = true;
      this.$http
        .get({
          reqName: `/reservation/paginated?UserId=${this.userID}`,
        })
        .then(({ data: response }) => {
          console.log(response.data);
          this.reservations = response.data;
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>
