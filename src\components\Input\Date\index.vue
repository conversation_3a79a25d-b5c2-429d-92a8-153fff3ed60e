<template>
  <form-group :name="name" :attribute="name" rules="required">
    <template slot-scope="{ attrs, listeners }">
      <v-datetime-picker
        v-bind="{ ...$attrs, ...attrs }"
        v-on="{ ...$listeners, ...listeners }"
        :datetime="dateValue"
        @input="handleChange"
        filled
        scrollable
        :text-field-props="{
          outlined: true,
          'error-messages':
            attrs['error-messages'].length > 0 ? attrs['error-messages'] : '',
        }"
        :datePickerProps="datePickerProps"
        dateFormat="yyyy-MM-dd"
        timeFormat="HH:mm"
      >
        <template v-slot:dateIcon>
          <v-icon>mdi-calendar</v-icon>
        </template>
        <template v-slot:timeIcon>
          <v-icon>mdi-clock</v-icon>
        </template>
      </v-datetime-picker>
    </template></form-group
  >
</template>


<script>
export default {
  data() {
    return {
      menu: false,
      dateValue: "",

      // i want to add min value for date
      datePickerProps: {
        min: new Date().toISOString().substr(0, 10),
        format: "DD-MM-YYYY hh:mm a",
      },
    };
  },
  props: {
    name: {
      type: String,
      default: () => "",
    },
    min: {
      type: String,
      default: "",
    },
    value: {
      type: String,
      default: "",
    },
  },
  methods: {
    handleChange(val) {
      if (this.serverErrors) {
        this.$store.dispatch("ClearServerErrors");
      }
      if (val instanceof Date) {
        this.$emit("input", val);
      }
    },
  },
  watch: {
    value: {
      immediate: true,
      deep: true,
      handler(val) {
        // i want to reset date value after submit form
        if (val) {
          this.dateValue = new Date(val);
        } else {
          this.dateValue = "";
        }
      },
    },
  },
};
</script>


<style lang="scss">
@import "index.scss";
</style>