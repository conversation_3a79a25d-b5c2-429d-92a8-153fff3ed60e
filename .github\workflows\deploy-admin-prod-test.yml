name: Deploy prod-test admin

on:
  push:
    branches:
      - prod-test  # Change to your branch if different

jobs:
  build-and-deploy:
    runs-on: windows-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Install Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install dependencies
        run: npm install

      - name: Build Vue.js app
        run: npm run build

      - name: Deploy via FTP
        uses: SamKirkland/FTP-Deploy-Action@v4.3.4
        with:
          server: ${{ secrets.FTP_SERVER_TEST }}
          username: ${{ secrets.FTP_USERNAME_TEST }}
          password: ${{ secrets.FTP_PASSWORD_TEST }}
          local-dir: dist/   # Adjust if your build folder is different
          server-dir: /wwwroot/  # Adjust if your MonsterASP path is different
