export const websiteRoutes = {
  path: "/",
  to: "/",
  icon: "mdi-application-cog",
  activeRoutes: ["record_footer", "record_carousel"],
  meta: {
    title: "website_settings",
    // permissions: "",
  },
  order: 3,
  allow_children: true,
  showInMenu: true,
  name: "settings",
  component: () => import("@/views/website"),
  children: [
    {
      path: "/footer",
      to: "/footer",
      component: () => import("@/views/website/footer/index"),
      name: "footer",
      icon: "mdi-flag",
      allowed: true,
      meta: {
        title: "footer",
        // permissions: 7
      },
      activeRoutes: ["record_footer"], // Specify the activeRoutes for the "cities" route

      children: [
        {
          path: "",
          component: () => import("@/views/website/footer/record/index"),
          name: "record_footer",
          icon: "arrow.svg",
          to: "/footer",
          allowed: true,
          meta: {
            title: "record_footer",
            // permissions: 7
          },
        }

      ],
    },
    {
      path: "/carousel",
      to: "/carousel",
      component: () => import("@/views/website/carousel/index"),
      name: "carousel",
      icon: "mdi-city",
      allowed: true,
      meta: {
        title: "carousel",
        // permissions: 7
      },
      activeRoutes: ["record_carousel"], // Specify the activeRoutes for the "cities" route

      children: [
        {
          path: "",
          component: () => import("@/views/website/carousel/record/index"),
          name: "record_carousel",
          icon: "arrow.svg",
          to: "/carousel",
          allowed: true,
          meta: {
            title: "record_carousel",
            // permissions: 7
          },
        }

      ],
    },



  ]
};
