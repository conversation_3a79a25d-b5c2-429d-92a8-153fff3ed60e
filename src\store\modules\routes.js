import { asyncRoutes } from "@/router/asyncRoutes";
import router from "@/router";
export default {
  state: () => ({
    routes: [],
  }),
  mutations: {
    SET_ROUTES(state, payload) {
      state.routes = payload;
    },
    SET_CREATE_PERMISSION(state, payload) {
      state.hasCreatePermission = payload;
    },
    SET_EDIT_PERMISSION(state, payload) {
      state.hasEditPermission = payload;
    },
    SET_DELETE_PERMISSION(state, payload) {
      state.hasDeletePermission = payload;
    },
    RESET_ROUTES(state) {
      state.routes = [];
    },

  },
  actions: {
    setAllowedRoutes({ commit }, permissions) {


      commit("SET_ROUTES", asyncRoutes);
    },

    resetRoutes({ commit }) {
      commit("RESET_ROUTES");
    },
  },
  getters: {
    getRoutes(state) {
      return asyncRoutes;
    },
  }
}