<template>
  <v-container class="mt-4">
    <v-row>
      <v-col cols="12" md="6" v-for="(item, index) in items" :key="index">
        <statistics-card
          :title="item.title"
          :count="item.count"
          :icon="item.icon"
          :color="item.color"
          :bgColor="item.bgColor"
        />
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12">
        <CustomTable
          :tableHeaders="headers"
          :reqName="`reservation/paginated?PageSize=5`"
          :showPagination="false"
          :slotColumnNames="[
            '#',
            'creationDate',
            'from',
            'fromDate',
            'to',
            'toDate',
            'pnr',
            'isActive',
            'isPaid',
            'isRefunded',

          ]"
          @handleClick="handleClick"
          :isClickable="true"
        >
          <template #item.#="{ item, index }">
            {{ index + pagination.from }}
          </template>
          <template #item.creationDate="{ item }">
            <span>{{
              convertDate(item.flights[0].creationDate, "LL HH:mm")
            }}</span>
          </template>
          <template #item.from="{ item }">
            <span>{{
              item.flights[0].flightDetails[0].departure_iataCode
            }}</span>
          </template>
          <template #item.fromDate="{ item }">
            <span>{{
              convertDate(
                item.flights[0].flightDetails[0].departure_at,
                "LL HH:mm"
              )
            }}</span>
          </template>

          <template #item.to="{ item }">
            <span>{{
              item.flights[0].flightDetails[
                item.flights[0].flightDetails.length - 1
              ].arrival_iataCode
            }}</span>
          </template>

          <template #item.toDate="{ item }">
            <span>{{
              convertDate(
                item.flights[0].flightDetails[
                  item.flights[0].flightDetails.length - 1
                ].arrival_at,
                "LL HH:mm"
              )
            }}</span>
          </template>
          <template #item.pnr="{ item }">
            <span>{{item.flights[0].reference}}</span>
          </template>
          
          <template #item.isActive="{ item }">
            <span>{{item.isActive ? "نعم" : "لا"}}</span>
          </template>
          <template #item.isPaid="{ item }">
            <span>{{item.isPaid ? "نعم" : "لا"}}</span>
          </template>
          <template #item.isRefunded="{ item }">
            <span>{{item.isRefunded ? "نعم" : "لا"}}</span>
          </template>
        </CustomTable>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  name: "Home",
  data() {
    return {
      stats: [],
    };
  },
  computed: {
    items() {
      return [
        {
          title: this.$t("total_tickets"),
          count: (this.stats?.totalTickets - this.stats?.totalOrderNotPaidAmount) || 0,
          icon: "mdi-cart",
          color: "#0da487",
          bgColor: "rgba(13, 164, 135, 0.1)",
        },
        {
          title: `${this.$t("total_orders")} (${(Number(this.stats?.totalTickets)) || 0})`,
          count: this.stats?.totalOrderAmount || 0,
          icon: "mdi-currency-usd",
          color: "#747dc6",
          bgColor: "rgba(116,125,198,0.1)",
        },
        {
          title: this.$t("completed_orders"),
          count: this.stats?.totalOrderPaidAmount  || 0,

          icon: "mdi-cash",
          color: "#ef3f3e",
          bgColor: "rgba(239,63,62,0.1)",
        },
        {
          title: `${this.$t("pending_orders")} (${(Number(this.stats?.totalOrderAmount) - Number( this.stats?.totalOrderPaidAmount)) || 0})`,
          count: this.stats?.totalOrderNotPaidAmount  || 0 ,
          icon: "mdi-timer-outline",
          color: "#9e65c2",
          bgColor: "rgba(158,101,194,0.1)",
        },
      ];
    },
    headers() {
      const headers = [
        {
          name: "#",
          value: "#",
          showColumn: true,
          width: 25,
        },
        {
          name: "creationDate",
          value: "creationDate",
          sortable: false,
          showColumn: true,
        },
        {
          name: "from",
          value: "from",
          sortable: false,
          showColumn: true,
        },
        {
          name: "fromDate",
          value: "fromDate",
          sortable: false,
          showColumn: true,
        },
        {
          name: "to",
          value: "to",
          sortable: false,
          showColumn: true,
        },
        {
          name: "toDate",
          value: "toDate",
          sortable: false,
          showColumn: true,
        },
        {
          name: "pnr",
          value: "pnr",
          sortable: false,
          showColumn: true,
        },
        {
          name: "نشطة",
          value: "isActive",
          sortable: false,
          showColumn: true,
        },
        {
          name: "مدفوعة",
          value: "isPaid",
          sortable: false,
          showColumn: true,
        },
        {
          name: "تم استردادها",
          value: "isRefunded",
          sortable: false,
          showColumn: true,
        }
      ];
      return headers.filter((head) => head.showColumn);
    },
  },
  methods: {
    handleClick(item) {
      this.$router.push(`/reservations/${item.reservationGUID}`);
    },
    async getStats() {
      const response = await this.$http.get({
        reqName: `reservation/get-statistics`,
      });
      this.stats = response.data.data;
    },
  },
  mounted() {
    this.getStats();
  },
};
</script>
