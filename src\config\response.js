import Vue from "vue";
import cookie from "js-cookie";
import { getToken } from "@/utils/auth";
import store from "@/store";

export const configureRequestHeaders = (config, store) => {
  if (store.getters.token) {
    config.headers["Authorization"] = `${"Bearer " + store.getters.token}`;
  }
  const timezoneOffset = -new Date().getTimezoneOffset() / 60;
  config.headers['Timezone'] = `${timezoneOffset + 12}`;

  // config.headers['Timezone'] =  `${-new Date().getTimezoneOffset() / 60}`;
  config.headers.common["Accept"] = "application/json"
  config.headers.common["Content-Type"] = "application/json";
  const flag = cookie.get("language") === "ar" ? 'EG' : 'us';
  config.headers.common["Accept-Language"] = `${cookie.get('language')}-${flag}`;

}

export const handleSuccessResponse = (response, store) => {

  const { method } = response.config;
  const allowedMethods = ["post", "put", "delete"];
  if (allowedMethods.includes(method)) {
    Vue.toasted.success(response.data.message);
  }
}
// Function to handle error responses
export const handleErrorResponse = (err) => {
  const { errors } = err.response.data;
  store.dispatch("SetServerErrors", errors);

  Vue.toasted.error(err.response?.data?.message);
}