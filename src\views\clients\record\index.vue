<template>
  <main class="mt-4">
    <v-container>
      <v-card class="page-card">
        <v-card-text>
          <v-row>
            <v-col cols="12">
              <CustomTable
                v-if="rendering"
                :tableHeaders="headers"
                reqName="user/paginated?GetActiveOnly=false&GetCustomers=true"
                :slotColumnNames="['#', 'isActive', 'imageURL']"
                :title="$t('clients')"
                @handleClick="handleClick"
                :isClickable="true"
              >
                <template #top-left>
                  <InputText
                    v-model="search"
                    name="search"
                    hide-details
                    outlined
                    class="mx-4"
                    dense
                    clearable
                    :label="$t('search')"
                    @input="setSearchQuery"
                  >
                  </InputText>
                </template>
                <template #item.#="{ item, index }">
                  {{ index + pagination.from }}
                </template>
                <template #item.imageURL="{ item }">
                  <v-img :src="item.imageURL" width="60" height="60"></v-img>
                </template>
                <template #item.isActive="{ item }">
                  <toggle-service
                    :is-edit="true"
                    :model-name="`${url}/activate-deactivate`"
                    :model-id="item.id"
                    v-model="item.isActive"
                  />
                </template>
              </CustomTable>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-container>
  </main>
</template>



<script>
import { mapActions } from "vuex";

export default {
  name: "ClientsRecord",
  data() {
    return {
      rendering: true,
      url: "user",
      search: "",
    };
  },
  computed: {
    headers() {
      const headers = [
        {
          name: "#",
          value: "#",
          showColumn: true,
        },
        {
          name: "image",
          value: "imageURL",
          sortable: false,
          showColumn: true,
        },
        {
          name: "name",
          value: "name",
          sortable: false,
          showColumn: true,
        },
        {
          name: "email",
          value: "email",
          sortable: false,
          showColumn: true,
        },
        {
          name: "phone",
          value: "phone",
          sortable: false,
          showColumn: true,
        },
        // {
        //   name: "roleName",
        //   value: "roleName",
        //   sortable: false,
        //   showColumn: true,
        // },
        {
          name: "active",
          value: "isActive",
          sortable: false,
          showColumn: true,
        },
      ];
      return headers.filter((head) => head.showColumn);
    },
  },
  methods: {
    ...mapActions(["setQuery"]),
    setSearchQuery() {
      console.log(this.search);
      let query = {
        SearchString: this.search,
      };
      this.setQuery(query);
      // this.rendering = false;
      // setTimeout(() => {
      //   this.rendering = true;
      // }, 10);
    },
    handleClick(item) {
      this.$router.push(`/clients/${item.id}`);
    },
  },
};
</script>