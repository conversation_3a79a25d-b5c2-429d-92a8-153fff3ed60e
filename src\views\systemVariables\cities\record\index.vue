<template>
  <main class="mt-4">
    <v-container>
      <v-card class="page-card">
        <v-card-text>
          <v-row>
            <v-col cols="12">
              <CustomTable
                v-if="rendering"
                :tableHeaders="headers"
                :reqName="`${url}/paginated?GetActiveOnly=false`"
                :slotColumnNames="['#', 'isActive', 'actions']"
                :title="$t('cities')"
              >
                <template #top-right>
                  <CityForm :url="url" :countries="countries" />
                </template>
                <template #top-left>
                  <InputText
                    v-model="search"
                    name="search"
                    hide-details
                    outlined
                    class="mx-4"
                    dense
                    clearable
                    :label="$t('search')"
                    @input="setSearchQuery"
                  >
                  </InputText>
                </template>
                <template #item.#="{ item, index }">
                  {{ index + pagination.from }}
                </template>
                <template #item.isActive="{ item }">
                  <toggle-service
                    :is-edit="true"
                    :model-name="`${url}/activate-deactivate`"
                    :model-id="item.id"
                    v-model="item.isActive"
                  />
                </template>
                <template #item.actions="{ item }">
                  <CityForm :url="url" :id="item.id" :countries="countries" />
                </template>
              </CustomTable>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-container>
  </main>
</template>



<script>
import CityForm from "@/components/Forms/City";
import { mapActions } from "vuex";

export default {
  name: "CitiesRecord",
  components: {
    CityForm,
  },
  data() {
    return {
      rendering: true,
      url: "city",
      countries: [],
    };
  },
  computed: {
    headers() {
      const headers = [
        {
          name: "#",
          value: "#",
          showColumn: true,
        },
        {
          name: "name",
          value: "name",
          sortable: false,
          showColumn: true,
        },
        {
          name: "code",
          value: "code",
          sortable: false,
          showColumn: true,
        },

        {
          name: "country_name",
          value: "countryName",
          sortable: false,
          showColumn: true,
        },

        {
          name: "active",
          value: "isActive",
          sortable: false,
          showColumn: true,
        },

        {
          name: "actions",
          value: "actions",
          showColumn: true,
        },
      ];
      return headers.filter((head) => head.showColumn);
    },
  },
  methods: {
    ...mapActions(["setQuery"]),
    setSearchQuery() {
      console.log(this.search);
      let query = {
        SearchString: this.search,
      };
      this.setQuery(query);
      // this.rendering = false;
      // setTimeout(() => {
      //   this.rendering = true;
      // }, 10);
    },
    getCountries() {
      this.$http
        .get({
          reqName: "country/paginated?pageSize=1000",
        })
        .then(({ data: response }) => {
          this.countries = response.data;
        });
    },
  },
  mounted() {
    this.getCountries();
  },
};
</script>