import { login, userInfo } from "@/api/login";
import { getToken, setToken, removeToken } from "@/utils/auth";
import Cookies from "js-cookie";
import router from "@/router";
const userCookie = Cookies.get('user');

const user = {
  state: {
    token: getToken(),
    language: Cookies.get("language") || "en",
    user: userCookie ? JSON.parse(userCookie) : null,
    permissions: JSON.parse(localStorage.getItem('permissions')) || [],
    userName: "",
  },
  mutations: {
    SET_USER(state, user) {
      state.user = user;
      console.log(user)
      Cookies.set("user", JSON.stringify(user));
    },
    SET_TOKEN(state, token) {
      state.token = token;
      setToken(token);
    },
    SET_PERMISSIONS(state, permissions) {
      state.permissions = permissions;
      localStorage.setItem('permissions', JSON.stringify(permissions));
    },
    SET_USERNAME(state, userName) {
      state.userName = userName
    },
    SET_RESET_TOKEN(state, resetToken) {
      state.token = resetToken
    }
  },
  actions: {
    Login({ commit, dispatch }, payload) {
      return new Promise((resolve, reject) => {
        login(payload)
          .then((response) => {
            const { data } = response.data;
            // if (data.userRoleSubModules.length === 0 || !data.branch.trim()) {
            //   reject("No branch found");

            //   return
            // }

            commit("SET_TOKEN", data.accessToken);
            resolve(response)
            // Set permissions and get user info sequentially
            dispatch("setPermissions")
            //   .then(() => dispatch("GetUserInfo"))
            //   .then(() => {
            //     // After permissions and user info are set, resolve the promise with the response
            //     resolve(response);
            //   })
            //   .catch((error) => {
            //     reject(error);
            //   });
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    GetUserInfo({ commit, dispatch }) {
      return new Promise((resolve, reject) => {
        userInfo()
          .then((res) => {
            const { data: user } = res.data;

            commit("SET_USER", user);
            resolve(user);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    setPermissions({ commit, dispatch }, permissions) {

      // commit("SET_PERMISSIONS", permissions);
      dispatch("setAllowedRoutes");
    },
    Logout({ commit, dispatch }) {
      return new Promise((resolve, reject) => {
        commit("SET_PERMISSIONS", [])
        commit("SET_TOKEN", "");
        removeToken();
        Cookies.remove("user");
        Cookies.remove("branchId")
        Cookies.remove("branchNameAr")
        Cookies.remove('branchNameEn')
        dispatch("resetRoutes");
        commit("SET_USER", null);

        return resolve();
      });
    },
    setUserInfo({ commit }, user) {
      commit("SET_USER", user);
    },
  },
  getters: {
    getPermissions(state) {
      return state.permissions;
    },
    getUser(state) {
      return state.user;
    },
    getUserName(state) {
      return state.userName;
    }
  },
};

export default user;
