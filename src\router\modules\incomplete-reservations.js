export const incompleteReservationsRoutes = {
  path: "/incomplete-reservations",
  to: "/incomplete-reservations",
  icon: "mdi-file-document-remove-outline",
  activeRoutes: ["incomplete_reservations_record"],
  meta: {
    title: "incomplete-reservations",
  },
  showInMenu: true,
  order: 9,
  component: () => import("@/views/incomplete-reservations"),
  children: [
    {
      path: "/",
      component: () => import("@/views/incomplete-reservations/record"),
      name: "incomplete_reservations_record",
      icon: "mdi-file-document-remove-outline",
      to: "/incomplete-reservations",
      allowed: false,
      meta: {
        title: "incomplete_reservations_record",
      },
    },
    {
      path: "/incomplete-reservations/:reservationGUID",
      name: "IncompleteReservationDetails",
      component: () => import("@/views/incomplete-reservations/reservationDetails"),
    },
  ],
};
