<template>
  <section>
    <div
      class="w-100 d-flex justify-end size-18 mb-3 primary--text pointer"
      @click="dialog = true"
    >
      {{ $t("forget_password") }}
    </div>

    <CustomDialog
      maxWidth="650"
      :dialog="dialog"
      @close-dialog="dialog = false"
    >
      <Stepper :stepper="stepper">
        <template #step-1>
          <AuthForgetPasswordEmailVerification
            @next-step="stepper = 2"
            @close-dialog="dialog = false"
          />
        </template>
        <template #step-2>
          <AuthForgetPasswordOtpVerification
            @next-step="stepper = 3"
            @close-dialog="dialog = false"
          />
        </template>
        <template #step-3>
          <AuthForgetPasswordNewPassword
            @close-dialog="dialog = false"
            @next-step="stepper = 1"
          />
        </template>
      </Stepper>
    </CustomDialog>
  </section>
</template>


<script>
import AuthForgetPasswordOtpVerification from "@/components/Auth/ForgetPassword/OtpVerification";
import AuthForgetPasswordNewPassword from "@/components/Auth/ForgetPassword/NewPassword";
import AuthForgetPasswordEmailVerification from "@/components/Auth/ForgetPassword/EmailVerification";
export default {
  name: "AuthForgetPassword",
  components: {
    AuthForgetPasswordOtpVerification,
    AuthForgetPasswordNewPassword,
    AuthForgetPasswordEmailVerification,
  },
  data() {
    return {
      dialog: false,
      stepper: 1,
    };
  },
};
</script>