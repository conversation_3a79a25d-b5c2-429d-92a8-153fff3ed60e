<template>
  <v-card elevation="0" class="pa-3">
    <div class="d-flex justify-space-between gap-sm mb-4 flex-wrap">
      <div class="d-flex align-center gap-sm">
        <h2 class="font-weight-bold">
          {{ title }}
        </h2>
        <!-- <TableSearch class="search" v-if="hasSearch" /> -->
        <slot name="top-left" />
      </div>
      <div class="d-flex gap-md align-center">
        <slot name="top-right" />
      </div>
    </div>
    <ShimmerLoader type="table" :loading="loading">
      <transition name="fade" mode="out-in">
        <v-data-table
          v-if="toggleTable"
          :mobile-breakpoint="0"
          :items="tableItems"
          :loading="loading"
          hide-default-footer
          class="table card-shadow"
          :items-per-page="per_page"
          :no-data-text="$t('no_data')"
          :loading-text="$t('loading')"
          v-bind="$attrs"
          v-on="$listeners"
          item-key="id"
          ref="table_ref"
          :headers="headers"
          @click:row="$emit('handleClick', $event)"
          :class="{ pointer: isClickable }"
          :expanded.sync="expanded"
        >
          <template
            v-for="name in slotColumnNames"
            v-slot:[`item.${name}`]="{ item, index }"
          >
            <slot :name="`item.${name}`" :item="item" :index="index"> </slot>
          </template>
          <template v-slot:expanded-item="{ headers, item }">
            <td :colspan="headers.length">
              <div class="expand-container">
                <span v-for="(header, index) in detailsHeaders" :key="index">
                  <slot
                    v-if="slotExpandNames.includes(header.value)"
                    :name="`expand-item.${header.value}`"
                    :item="item"
                    :text="header.name"
                  >
                  </slot>
                  <p v-else>{{ header.name }}: {{ item[header.value] }}</p>
                </span>
              </div>
            </td>
          </template>
          <template slot="no-data">
            {{ $t(`no_data`) }}
          </template>
          <template #body.prepend>
            <slot name="prepend"></slot>
          </template>
        </v-data-table>
      </transition>
      <v-row class="my-3 mb-10" v-if="showPagination">
        <v-col cols="12" md="10" class="align-self-center">
          <Pagination />
        </v-col>
        <v-col cols="12" md="2" class="align-self-end">
          <InputSelect
            :items="options"
            outlined
            v-model="per_page"
            style="max-width: 100px"
            hide-details
            dense
            itemText="text"
            itemValue="value"
            @change="changePerPage"
          />
        </v-col>
      </v-row>
    </ShimmerLoader>
  </v-card>
</template>


<script>
import { mapGetters, mapActions } from "vuex";
import { isEqual } from "lodash";
export default {
  name: "CustomTable",
  inheritAttrs: false,
  props: {
    reqName: {
      type: String,
      default: "",
    },
    tableHeaders: {
      type: Array,
      default: () => [],
    },
    itemsData: {
      type: Array,
      default: () => [],
    },
    fromStore: {
      type: Boolean,
      default: true,
    },
    showPagination: {
      type: Boolean,
      default: true,
    },
    slotColumnNames: {
      type: Array,
      default: () => [],
    },
    isClickable: {
      type: Boolean,
      default: false,
    },
    slotExpandNames: {
      type: Array,
      default: () => [],
    },
    detailsHeaders: {
      type: Array,
      default: () => [],
    },
    title: {
      type: String,
      default: () => "",
    },
  },
  data() {
    return {
      headers: [],
      loading: true,
      sortDesc: true,
      toggleTable: true,
      queries: {},
      per_page: 10,
      expanded: [],
    };
  },
  computed: {
    ...mapGetters({
      items: "getsTableItems",
      pagination: "getPagination",
    }),
    tableItems() {
      if (this.fromStore) {
        return this.items;
      } else {
        return this.itemsData;
      }
    },
    options() {
      return [
        {
          text: "10",
          value: 10,
        },
        {
          text: "25",
          value: 25,
        },
        {
          text: "50",
          value: 50,
        },
        {
          text: "100",
          value: 100,
        },

        {
          text: this.$t("all"),
          value: this.pagination.totalCount,
        },
      ];
    },
  },
  mounted() {
    this.$eventBus.$on("on-refresh", () => {
      this.toggleTable = false;
      setTimeout(() => {
        this.toggleTable = true;
      }, 0);
    });
    this.$eventBus.$on("on-refetch-data", () => {
      this.per_page = 10;
      this.getTableData();
    });
  },
  created() {
    this.createTableHeaders();
    if (this.reqName) {
      this.getTableData();
    }
  },
  methods: {
    ...mapActions({ getTableItems: "getTableItems" }),
    getTableData() {
      this.getTableItems({ reqName: this.reqName })
        .then(() => {
          this.loading = false;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    generateTableHeaders(headersName = []) {
      return headersName.map((header) => {
        return {
          text: header ? this.$t(`${header.name}`) : "",
          value: header.value,
          align: "center",
          sortable: header.sortable ? header.sortable : false,
        };
      });
    },
    createTableHeaders() {
      this.headers = this.generateTableHeaders(this.tableHeaders);
    },
    changePerPage() {
      this.queries = {
        ...this.queries,
        ...this.getQuery,
        PageSize: this.per_page,
      };
      if (!isEqual(this.queries, this.getQuery)) {
        this.$store.dispatch("setQuery", this.queries);
        window.scrollTo({ top: 0, behavior: "smooth" });
      }
    },
  },
  watch: {
    getQuery: {
      handler(query) {
        if (query && Object.keys(query).length > 0) {
          this.getTableData();
        }
      },
      immediate: false,
    },
    reqName: {
      handler(value) {
        if (!value) this.loading = false;
      },
      immediate: true,
    },
  },
};
</script>


<style lang="scss">
@import "index.scss";
</style>